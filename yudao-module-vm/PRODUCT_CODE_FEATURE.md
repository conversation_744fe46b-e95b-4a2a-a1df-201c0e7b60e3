# 产品编码选择功能说明

## 功能概述

为虚拟卡创建表单添加了产品编码选择功能，用户可以通过弹窗选择器来选择产品编码，而不需要手动输入。

## 新增接口

### 后端接口

1. **VmCardIoApiClient.getProductCodes()**
   - 调用 `/getProductCode` 接口获取产品编码列表
   - 返回格式与 `getCardProducts()` 相同

2. **VirtualCardController.getProductCodes()**
   - 路径: `GET /vm/virtual-card/product-codes`
   - 参数: `providerType` (可选)
   - 返回: `List<CardProductResponseDTO>`

3. **VirtualCardService.getProductCodes()**
   - 业务逻辑处理
   - 支持指定厂商类型或使用默认厂商

### 前端接口

1. **CardApi.getProductCodes()**
   - 调用后端接口获取产品编码列表
   - 支持传入厂商类型参数

## 新增组件

### ProductCodeSelector.vue

产品编码选择器组件，提供以下功能：

#### 功能特性
- **卡片式展示**: 以卡片形式展示每个产品编码
- **搜索过滤**: 支持按产品编码、卡类型、网络进行搜索
- **选择交互**: 点击卡片选择，支持单选
- **响应式布局**: 自适应网格布局
- **加载状态**: 显示加载动画和空状态

#### 组件属性
```typescript
interface Props {
  modelValue: boolean        // 弹窗显示状态
  providerType?: number     // 厂商类型
}
```

#### 组件事件
```typescript
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', product: CardProductVO): void
}
```

#### 数据结构
```typescript
interface CardProductVO {
  bin: string              // BIN号码
  productCode: string      // 产品编码
  type: string            // 卡类型 (save/share)
  network: string         // 卡网络 (VISA/MASTERCARD)
  media: string           // 卡介质 (virtual_card/physical_card)
  issuingArea: string     // 发卡地区
  // ... 其他可选字段
}
```

## 表单集成

### VirtualCardForm.vue 修改

1. **产品编码字段改为只读**
   - 添加搜索按钮
   - 点击打开产品选择器

2. **依赖厂商类型**
   - 必须先选择厂商类型才能选择产品编码
   - 根据厂商类型加载对应的产品列表

3. **选择流程**
   ```
   选择厂商类型 → 点击产品编码字段 → 打开选择器 → 选择产品 → 确认
   ```

## API响应格式

### VMCardIO /getProductCode 响应
```json
{
  "code": 0,
  "msg": "ok", 
  "data": {
    "list": [
      {
        "bin": "433451",
        "product_code": "Q5334PH",
        "type": "save",
        "network": "VISA",
        "media": "virtual_card",
        "issuing_area": "USA"
      }
    ]
  }
}
```

### 标准化后的响应
```json
{
  "code": 0,
  "msg": "成功",
  "data": [
    {
      "bin": "433451",
      "productCode": "Q5334PH", 
      "type": "save",
      "network": "VISA",
      "media": "virtual_card",
      "issuingArea": "USA"
    }
  ]
}
```

## 样式设计

### 卡片样式
- **默认状态**: 灰色边框，白色背景
- **悬停状态**: 蓝色边框，阴影效果
- **选中状态**: 蓝色边框，浅蓝背景，右上角勾选图标

### 布局特点
- 响应式网格布局，最小宽度300px
- 最大高度400px，超出滚动
- 16px间距，8px圆角

## 使用说明

### 开发者使用
```vue
<template>
  <ProductCodeSelector
    v-model="selectorVisible"
    :provider-type="providerType"
    @confirm="handleProductSelect"
  />
</template>

<script setup>
const selectorVisible = ref(false)
const providerType = ref(1)

const handleProductSelect = (product) => {
  console.log('选择的产品:', product)
  // 处理选择结果
}
</script>
```

### 用户使用流程
1. 在创建虚拟卡表单中选择厂商类型
2. 点击产品编码输入框或搜索按钮
3. 在弹出的选择器中浏览产品列表
4. 可使用搜索功能快速定位产品
5. 点击目标产品卡片进行选择
6. 点击确定按钮完成选择

## 注意事项

1. **厂商依赖**: 必须先选择厂商类型才能获取产品列表
2. **错误处理**: 网络错误时显示友好提示
3. **性能优化**: 产品列表较大时考虑分页或虚拟滚动
4. **缓存策略**: 可考虑缓存产品列表减少API调用

## 扩展建议

1. **收藏功能**: 支持收藏常用产品编码
2. **历史记录**: 记录最近使用的产品编码
3. **批量选择**: 支持多选模式（如果业务需要）
4. **产品详情**: 点击查看产品详细信息
5. **排序功能**: 支持按不同字段排序
