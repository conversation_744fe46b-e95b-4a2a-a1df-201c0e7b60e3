package cn.iocoder.yudao.module.vm.util;

import cn.hutool.core.codec.Base64;
import lombok.extern.slf4j.Slf4j;

/**
 * 私钥格式转换工具
 * 
 * 用于在PKCS#1和PKCS#8格式之间转换RSA私钥
 */
@Slf4j
public class PrivateKeyConverter {

    /**
     * 将PKCS#1格式私钥转换为PKCS#8格式
     * 
     * @param pkcs1PrivateKey PKCS#1格式的私钥字符串
     * @return PKCS#8格式的私钥字符串
     */
    public static String convertPKCS1ToPKCS8(String pkcs1PrivateKey) {
        try {
            // 清理输入
            String cleanKey = pkcs1PrivateKey
                    .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                    .replace("-----END RSA PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");
            
            byte[] pkcs1Bytes = Base64.decode(cleanKey);
            byte[] pkcs8Bytes = convertPKCS1BytesToPKCS8(pkcs1Bytes);
            String pkcs8Base64 = Base64.encode(pkcs8Bytes);
            
            // 格式化输出
            StringBuilder result = new StringBuilder();
            result.append("-----BEGIN PRIVATE KEY-----\n");
            
            // 每64个字符换行
            for (int i = 0; i < pkcs8Base64.length(); i += 64) {
                int end = Math.min(i + 64, pkcs8Base64.length());
                result.append(pkcs8Base64, i, end).append("\n");
            }
            
            result.append("-----END PRIVATE KEY-----");
            return result.toString();
            
        } catch (Exception e) {
            log.error("PKCS#1转PKCS#8失败", e);
            throw new RuntimeException("私钥格式转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将PKCS#8格式私钥转换为PKCS#1格式
     * 
     * @param pkcs8PrivateKey PKCS#8格式的私钥字符串
     * @return PKCS#1格式的私钥字符串
     */
    public static String convertPKCS8ToPKCS1(String pkcs8PrivateKey) {
        try {
            // 清理输入
            String cleanKey = pkcs8PrivateKey
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");
            
            byte[] pkcs8Bytes = Base64.decode(cleanKey);
            byte[] pkcs1Bytes = extractPKCS1FromPKCS8(pkcs8Bytes);
            String pkcs1Base64 = Base64.encode(pkcs1Bytes);
            
            // 格式化输出
            StringBuilder result = new StringBuilder();
            result.append("-----BEGIN RSA PRIVATE KEY-----\n");
            
            // 每64个字符换行
            for (int i = 0; i < pkcs1Base64.length(); i += 64) {
                int end = Math.min(i + 64, pkcs1Base64.length());
                result.append(pkcs1Base64, i, end).append("\n");
            }
            
            result.append("-----END RSA PRIVATE KEY-----");
            return result.toString();
            
        } catch (Exception e) {
            log.error("PKCS#8转PKCS#1失败", e);
            throw new RuntimeException("私钥格式转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将PKCS#1字节数组转换为PKCS#8字节数组
     */
    private static byte[] convertPKCS1BytesToPKCS8(byte[] pkcs1Bytes) {
        // RSA算法标识符
        byte[] rsaOID = {
            0x30, 0x0D, // SEQUENCE, length 13
            0x06, 0x09, 0x2A, (byte)0x86, 0x48, (byte)0x86, (byte)0xF7, 0x0D, 0x01, 0x01, 0x01, // RSA OID
            0x05, 0x00  // NULL
        };

        // 计算总长度
        int totalLength = 1 + 1 + 1 + rsaOID.length + getLengthBytes(pkcs1Bytes.length).length + pkcs1Bytes.length;
        byte[] lengthBytes = getLengthBytes(totalLength);
        
        // 构建PKCS#8结构
        byte[] pkcs8 = new byte[1 + lengthBytes.length + totalLength];
        int offset = 0;
        
        // SEQUENCE tag
        pkcs8[offset++] = 0x30;
        
        // Length
        System.arraycopy(lengthBytes, 0, pkcs8, offset, lengthBytes.length);
        offset += lengthBytes.length;
        
        // Version (INTEGER 0)
        pkcs8[offset++] = 0x02;
        pkcs8[offset++] = 0x01;
        pkcs8[offset++] = 0x00;
        
        // Algorithm identifier
        System.arraycopy(rsaOID, 0, pkcs8, offset, rsaOID.length);
        offset += rsaOID.length;
        
        // Private key (OCTET STRING)
        pkcs8[offset++] = 0x04;
        byte[] keyLengthBytes = getLengthBytes(pkcs1Bytes.length);
        System.arraycopy(keyLengthBytes, 0, pkcs8, offset, keyLengthBytes.length);
        offset += keyLengthBytes.length;
        System.arraycopy(pkcs1Bytes, 0, pkcs8, offset, pkcs1Bytes.length);
        
        return pkcs8;
    }

    /**
     * 从PKCS#8字节数组中提取PKCS#1字节数组
     */
    private static byte[] extractPKCS1FromPKCS8(byte[] pkcs8Bytes) {
        // 简化的PKCS#8解析，假设结构正确
        // 实际应用中可能需要更严格的ASN.1解析
        
        int offset = 0;
        
        // 跳过SEQUENCE tag
        if (pkcs8Bytes[offset] != 0x30) {
            throw new IllegalArgumentException("不是有效的PKCS#8格式");
        }
        offset++;
        
        // 跳过长度
        offset += skipLength(pkcs8Bytes, offset);
        
        // 跳过version
        if (pkcs8Bytes[offset] == 0x02) {
            offset++;
            offset += skipLength(pkcs8Bytes, offset);
            offset++; // 跳过version值
        }
        
        // 跳过算法标识符
        if (pkcs8Bytes[offset] == 0x30) {
            offset++;
            int algIdLength = getLength(pkcs8Bytes, offset);
            offset += getLengthBytes(algIdLength).length;
            offset += algIdLength;
        }
        
        // 找到私钥数据（OCTET STRING）
        if (pkcs8Bytes[offset] == 0x04) {
            offset++;
            int keyLength = getLength(pkcs8Bytes, offset);
            offset += getLengthBytes(keyLength).length;
            
            byte[] pkcs1Bytes = new byte[keyLength];
            System.arraycopy(pkcs8Bytes, offset, pkcs1Bytes, 0, keyLength);
            return pkcs1Bytes;
        }
        
        throw new IllegalArgumentException("无法从PKCS#8中提取PKCS#1数据");
    }

    /**
     * 获取ASN.1长度编码
     */
    private static byte[] getLengthBytes(int length) {
        if (length < 0x80) {
            return new byte[]{(byte) length};
        } else if (length < 0x100) {
            return new byte[]{(byte) 0x81, (byte) length};
        } else if (length < 0x10000) {
            return new byte[]{(byte) 0x82, (byte) (length >> 8), (byte) length};
        } else {
            return new byte[]{(byte) 0x83, (byte) (length >> 16), (byte) (length >> 8), (byte) length};
        }
    }

    /**
     * 解析ASN.1长度
     */
    private static int getLength(byte[] data, int offset) {
        int firstByte = data[offset] & 0xFF;
        if (firstByte < 0x80) {
            return firstByte;
        } else {
            int lengthBytes = firstByte & 0x7F;
            int length = 0;
            for (int i = 1; i <= lengthBytes; i++) {
                length = (length << 8) | (data[offset + i] & 0xFF);
            }
            return length;
        }
    }

    /**
     * 跳过ASN.1长度字段
     */
    private static int skipLength(byte[] data, int offset) {
        int firstByte = data[offset] & 0xFF;
        if (firstByte < 0x80) {
            return 1;
        } else {
            return 1 + (firstByte & 0x7F);
        }
    }

    /**
     * 主方法，用于命令行转换
     */
    public static void main(String[] args) {
        if (args.length != 2) {
            System.out.println("用法: java PrivateKeyConverter <input_file> <output_format>");
            System.out.println("output_format: pkcs1 或 pkcs8");
            return;
        }
        
        // 这里可以添加文件读取和转换逻辑
        System.out.println("私钥格式转换工具");
        System.out.println("请使用 convertPKCS1ToPKCS8() 或 convertPKCS8ToPKCS1() 方法");
    }
}
