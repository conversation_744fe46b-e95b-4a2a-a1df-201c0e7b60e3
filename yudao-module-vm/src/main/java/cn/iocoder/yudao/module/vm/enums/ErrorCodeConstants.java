package cn.iocoder.yudao.module.vm.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * VM 错误码枚举类
 * 
 * vm 系统，使用 1-003-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 虚拟卡相关 1-003-001-000 ==========
    ErrorCode VIRTUAL_CARD_NOT_EXISTS = new ErrorCode(1_003_001_000, "虚拟卡不存在");
    ErrorCode VIRTUAL_CARD_STATUS_INVALID = new ErrorCode(1_003_001_001, "虚拟卡状态无效");
    ErrorCode VIRTUAL_CARD_BALANCE_INSUFFICIENT = new ErrorCode(1_003_001_002, "虚拟卡余额不足");
    ErrorCode VIRTUAL_CARD_EXPIRED = new ErrorCode(1_003_001_003, "虚拟卡已过期");
    ErrorCode VIRTUAL_CARD_FROZEN = new ErrorCode(1_003_001_004, "虚拟卡已冻结");
    ErrorCode VIRTUAL_CARD_DELETED = new ErrorCode(1_003_001_005, "虚拟卡已删除");

    // ========== 厂商相关 1-003-002-000 ==========
    ErrorCode PROVIDER_NOT_EXISTS = new ErrorCode(1_003_002_000, "厂商不存在");
    ErrorCode PROVIDER_DISABLED = new ErrorCode(1_003_002_001, "厂商已禁用");
    ErrorCode PROVIDER_CONFIG_INVALID = new ErrorCode(1_003_002_002, "厂商配置无效");
    ErrorCode PROVIDER_API_ERROR = new ErrorCode(1_003_002_003, "厂商API调用失败");
    ErrorCode PROVIDER_ENCRYPT_ERROR = new ErrorCode(1_003_002_004, "厂商数据加密失败");
    ErrorCode PROVIDER_DECRYPT_ERROR = new ErrorCode(1_003_002_005, "厂商数据解密失败");

    // ========== 交易相关 1-003-003-000 ==========
    ErrorCode TRANSACTION_NOT_EXISTS = new ErrorCode(1_003_003_000, "交易记录不存在");
    ErrorCode TRANSACTION_STATUS_INVALID = new ErrorCode(1_003_003_001, "交易状态无效");
    ErrorCode TRANSACTION_AMOUNT_INVALID = new ErrorCode(1_003_003_002, "交易金额无效");

    // ========== 同步相关 1-003-004-000 ==========
    ErrorCode SYNC_FAILED = new ErrorCode(1_003_004_000, "数据同步失败");
    ErrorCode SYNC_CONFLICT = new ErrorCode(1_003_004_001, "数据同步冲突");
    ErrorCode SYNC_TIMEOUT = new ErrorCode(1_003_004_002, "数据同步超时");

}
