package cn.iocoder.yudao.module.vm.api.encryption;

/**
 * 加密策略接口
 * 
 * 不同厂商可以实现不同的加密方式
 */
public interface EncryptionStrategy {

    /**
     * 加密数据
     * 
     * @param data 原始数据
     * @param publicKey 公钥
     * @return 加密后的数据
     */
    String encrypt(String data, String publicKey);

    /**
     * 解密数据
     * 
     * @param encryptedData 加密数据
     * @param privateKey 私钥
     * @return 解密后的数据
     */
    String decrypt(String encryptedData, String privateKey);

    /**
     * 获取加密类型
     * 
     * @return 加密类型
     */
    String getEncryptionType();

}
