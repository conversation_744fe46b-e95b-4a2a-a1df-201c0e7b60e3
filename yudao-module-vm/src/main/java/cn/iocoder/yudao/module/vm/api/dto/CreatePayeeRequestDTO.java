package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;

/**
 * 创建收款方请求DTO
 */
@Data
public class CreatePayeeRequestDTO {

    /**
     * 收款方名称
     */
    private String payeeName;

    /**
     * 收款方类型
     */
    private String payeeType;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 收款方地址
     */
    private String address;

    /**
     * 城市
     */
    private String city;

    /**
     * 国家
     */
    private String country;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

}
