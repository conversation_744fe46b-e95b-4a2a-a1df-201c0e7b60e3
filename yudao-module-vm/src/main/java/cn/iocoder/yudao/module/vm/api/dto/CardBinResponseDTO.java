package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 卡Bin响应DTO
 */
@Data
public class CardBinResponseDTO {

    /**
     * Bin ID
     */
    private String binId;

    /**
     * Bin号码
     */
    private String binNumber;

    /**
     * 卡类型
     */
    private String cardType;

    /**
     * 卡品牌
     */
    private String cardBrand;

    /**
     * 发卡行
     */
    private String issuer;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 费用
     */
    private BigDecimal fee;

    /**
     * 限制信息
     */
    private String restrictions;

    /**
     * 是否启用
     */
    private Boolean enabled;

}
