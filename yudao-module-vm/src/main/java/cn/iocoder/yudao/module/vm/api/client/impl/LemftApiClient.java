package cn.iocoder.yudao.module.vm.api.client.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.vm.api.client.ConfigurableApiClient;
import cn.iocoder.yudao.module.vm.api.client.VirtualCardApiClient;
import cn.iocoder.yudao.module.vm.api.dto.*;
import cn.iocoder.yudao.module.vm.api.encryption.EncryptionStrategyFactory;
import cn.iocoder.yudao.module.vm.dal.dataobject.ProviderConfigDO;
import cn.iocoder.yudao.module.vm.enums.ProviderTypeEnum;
import cn.iocoder.yudao.module.vm.service.provider.ProviderConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vm.enums.ErrorCodeConstants.*;

/**
 * LEMFT API客户端实现
 * 
 * 实现LEMFT厂商的虚拟卡API接口
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LemftApiClient implements VirtualCardApiClient, ConfigurableApiClient {

    private final ProviderConfigService providerConfigService;
    private final EncryptionStrategyFactory encryptionStrategyFactory;

    // 缓存的厂商配置
    private volatile ProviderConfigDO cachedConfig;
    private volatile long lastConfigUpdateTime = 0;

    @Override
    public Integer getProviderType() {
        return ProviderTypeEnum.LEMFT.getType();
    }

    @Override
    public TokenResult getAccessToken() {
        try {
            ProviderConfigDO config = getProviderConfig();

            // LEMFT的token获取逻辑（根据实际API文档实现）
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("app_id", config.getAppId());
            requestData.put("app_secret", config.getAppSecret());

            String url = config.getBaseUrl() + "/auth/token";
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(requestData))
                    .timeout(config.getTimeout() * 1000)
                    .execute();

            if (!response.isOk()) {
                return TokenResult.failure("HTTP请求失败: " + response.getStatus());
            }

            // 解析token响应（根据实际响应格式调整）
            String responseBody = response.body();
            Map<String, Object> responseMap = JSONUtil.parseObj(responseBody);
            
            if (responseMap.containsKey("access_token")) {
                String accessToken = (String) responseMap.get("access_token");
                Integer expiresIn = (Integer) responseMap.get("expires_in");
                
                // 保存token到配置
                saveTokenToConfig(config, accessToken, null, expiresIn);
                
                return TokenResult.success(accessToken, "Bearer", expiresIn, null);
            } else {
                return TokenResult.failure("获取token失败: " + responseBody);
            }

        } catch (Exception e) {
            log.error("获取LEMFT访问令牌失败", e);
            return TokenResult.failure("获取访问令牌失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isTokenValid() {
        ProviderConfigDO config = getProviderConfig();
        return StrUtil.isNotBlank(config.getAccessToken()) && 
               config.getTokenExpireTime() != null && 
               config.getTokenExpireTime().isAfter(java.time.LocalDateTime.now().plusMinutes(5));
    }

    @Override
    public ApiResponseDTO<AccountBalanceResponseDTO> getAccountBalance() {
        try {
            // LEMFT使用钱包列表来获取余额信息
            ApiResponseDTO<List<WalletResponseDTO>> walletListResponse = getWalletList();
            if (!walletListResponse.isSuccess() || walletListResponse.getData() == null || walletListResponse.getData().isEmpty()) {
                return ApiResponseDTO.error("无可用钱包");
            }
            return convertWalletToBalance(walletListResponse.getData().get(0));
        } catch (Exception e) {
            log.error("获取账户余额失败", e);
            return ApiResponseDTO.error("获取账户余额失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<List<WalletResponseDTO>> getWalletList() {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            // 根据LEMFT API文档添加必要参数
            
            // 发送请求
            String responseBody = sendRequest(config, "/wallet/list", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("获取钱包列表失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<CreateCardResponseDTO> createCard(CreateCardRequestDTO request) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数（根据LEMFT API文档调整）
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("cardholder_id", request.getCardholderId()); // LEMFT需要先创建持卡人
            bizContent.put("card_type", request.getProductCode());
            bizContent.put("amount", request.getAmount());
            // 添加其他必要参数...
            
            // 发送请求
            String responseBody = sendRequest(config, "/card/create", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("创建卡片失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<CardDetailResponseDTO> getCardDetail(CardDetailRequestDTO request) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", request.getCardId());
            
            // 发送请求
            String responseBody = sendRequest(config, "/card/detail", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("获取卡片详情失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<Void> freezeCard(String cardId) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", cardId);
            
            // 发送请求
            String responseBody = sendRequest(config, "/card/freeze", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("冻结卡片失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<Void> unfreezeCard(String cardId) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", cardId);
            
            // 发送请求
            String responseBody = sendRequest(config, "/card/unfreeze", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("解冻卡片失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<Void> deleteCard(String cardId) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", cardId);
            
            // 发送请求
            String responseBody = sendRequest(config, "/card/cancel", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("销卡失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<Void> rechargeCard(String cardId, BigDecimal amount) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", cardId);
            bizContent.put("amount", amount);
            
            // 发送请求
            String responseBody = sendRequest(config, "/card/recharge", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("卡片充值失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public void refreshConfig() {
        synchronized (this) {
            cachedConfig = null;
            lastConfigUpdateTime = 0;
        }
        getProviderConfig();
    }

    /**
     * 获取厂商配置
     */
    private ProviderConfigDO getProviderConfig() {
        // 配置缓存逻辑（与VmCardIoApiClient类似）
        if (cachedConfig == null || System.currentTimeMillis() - lastConfigUpdateTime > 300000) {
            synchronized (this) {
                if (cachedConfig == null || System.currentTimeMillis() - lastConfigUpdateTime > 300000) {
                    cachedConfig = providerConfigService.getByProviderType(getProviderType());
                    lastConfigUpdateTime = System.currentTimeMillis();
                    
                    if (cachedConfig == null) {
                        throw exception(PROVIDER_NOT_EXISTS, "LEMFT厂商配置不存在");
                    }
                }
            }
        }
        return cachedConfig;
    }

    /**
     * 发送请求
     */
    private String sendRequest(ProviderConfigDO config, String endpoint, Map<String, Object> bizContent) {
        try {
            // 确保token有效
            ensureTokenValid(config);

            // 根据LEMFT的加密策略进行加密（如果需要）
            String requestBody = JSONUtil.toJsonStr(bizContent);
            
            // 发送HTTP请求
            String url = config.getBaseUrl() + endpoint;
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + config.getAccessToken())
                    .body(requestBody)
                    .timeout(config.getTimeout() * 1000)
                    .execute();

            if (!response.isOk()) {
                throw new RuntimeException("HTTP请求失败: " + response.getStatus());
            }

            return response.body();

        } catch (Exception e) {
            log.error("发送请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("发送请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 确保token有效
     */
    private void ensureTokenValid(ProviderConfigDO config) {
        if (!isTokenValid()) {
            TokenResult newTokenResult = getAccessToken();
            if (!newTokenResult.isSuccess()) {
                throw new RuntimeException("无法获取有效的访问令牌: " + newTokenResult.getErrorMessage());
            }
        }
    }

    /**
     * 保存token到配置
     */
    private void saveTokenToConfig(ProviderConfigDO config, String accessToken, String refreshToken, Integer expiresIn) {
        config.setAccessToken(accessToken);
        config.setRefreshToken(refreshToken);
        if (expiresIn != null) {
            config.setTokenExpireTime(java.time.LocalDateTime.now().plusSeconds(expiresIn));
        }
        providerConfigService.updateProviderToken(config.getId(),
            accessToken, refreshToken, config.getTokenExpireTime());
    }

    /**
     * 将钱包信息转换为余额信息
     */
    private ApiResponseDTO<AccountBalanceResponseDTO> convertWalletToBalance(WalletResponseDTO wallet) {
        AccountBalanceResponseDTO balance = new AccountBalanceResponseDTO();
        balance.setBalance(wallet.getBalance());
        balance.setAvailableBalance(wallet.getAvailableBalance());
        balance.setFrozenBalance(wallet.getFrozenBalance());
        return ApiResponseDTO.success(balance);
    }

}
