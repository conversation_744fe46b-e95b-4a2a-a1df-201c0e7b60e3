package cn.iocoder.yudao.module.vm.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.vm.controller.admin.card.vo.VirtualCardPageReqVO;
import cn.iocoder.yudao.module.vm.dal.dataobject.VirtualCardDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 虚拟卡 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface VirtualCardMapper extends BaseMapperX<VirtualCardDO> {

    default PageResult<VirtualCardDO> selectPage(VirtualCardPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VirtualCardDO>()
                .likeIfPresent(VirtualCardDO::getCardNumber, reqVO.getCardNumber())
                .likeIfPresent(VirtualCardDO::getHolderName, reqVO.getHolderName())
                .eqIfPresent(VirtualCardDO::getStatus, reqVO.getStatus())
                .eqIfPresent(VirtualCardDO::getProviderType, reqVO.getProviderType())
                .likeIfPresent(VirtualCardDO::getLabel, reqVO.getLabel())
                .betweenIfPresent(VirtualCardDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VirtualCardDO::getId));
    }

    default VirtualCardDO selectByThirdPartyCardId(String thirdPartyCardId) {
        return selectOne(VirtualCardDO::getThirdPartyCardId, thirdPartyCardId);
    }

    default List<VirtualCardDO> selectListByProviderType(Integer providerType) {
        return selectList(VirtualCardDO::getProviderType, providerType);
    }

    default List<VirtualCardDO> selectListByStatus(Integer status) {
        return selectList(VirtualCardDO::getStatus, status);
    }

    default List<VirtualCardDO> selectListByHolderName(String holderName) {
        return selectList(new LambdaQueryWrapperX<VirtualCardDO>()
                .like(VirtualCardDO::getHolderName, holderName));
    }

}
