package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 持卡人列表请求DTO
 */
@Data
public class CardholderListRequestDTO {

    /**
     * 持卡人姓名
     */
    private String name;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;

}
