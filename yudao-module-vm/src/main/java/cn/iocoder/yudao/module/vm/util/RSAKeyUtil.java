package cn.iocoder.yudao.module.vm.util;

import cn.hutool.core.codec.Base64;
import lombok.extern.slf4j.Slf4j;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * RSA密钥工具类
 * 
 * 支持多种格式的RSA密钥加载
 */
@Slf4j
public class RSAKeyUtil {

    private static final String RSA_ALGORITHM = "RSA";

    /**
     * 加载公钥
     * 
     * @param publicKeyStr 公钥字符串
     * @return 公钥对象
     * @throws Exception 加载失败
     */
    public static PublicKey loadPublicKey(String publicKeyStr) throws Exception {
        String publicKeyContent = publicKeyStr
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replace("-----BEGIN RSA PUBLIC KEY-----", "")
                .replace("-----END RSA PUBLIC KEY-----", "")
                .replaceAll("\\s", "");
        
        byte[] keyBytes = Base64.decode(publicKeyContent);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 加载私钥（支持PKCS#1和PKCS#8格式）
     * 
     * @param privateKeyStr 私钥字符串
     * @return 私钥对象
     * @throws Exception 加载失败
     */
    public static PrivateKey loadPrivateKey(String privateKeyStr) throws Exception {
        // 清理私钥字符串
        String privateKeyContent = cleanPrivateKeyString(privateKeyStr);
        byte[] keyBytes = Base64.decode(privateKeyContent);
        
        // 首先尝试PKCS#8格式
        try {
            return loadPKCS8PrivateKey(keyBytes);
        } catch (Exception e) {
            log.debug("PKCS#8格式解析失败，尝试PKCS#1格式: {}", e.getMessage());
        }
        
        // 尝试PKCS#1格式
        try {
            return loadPKCS1PrivateKey(keyBytes);
        } catch (Exception e) {
            log.error("私钥格式不支持: {}", e.getMessage());
            throw new Exception("私钥格式错误，支持的格式：PKCS#1 (-----BEGIN RSA PRIVATE KEY-----) 或 PKCS#8 (-----BEGIN PRIVATE KEY-----)", e);
        }
    }

    /**
     * 清理私钥字符串
     */
    private static String cleanPrivateKeyString(String privateKeyStr) {
        return privateKeyStr
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                .replace("-----END RSA PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
    }

    /**
     * 加载PKCS#8格式私钥
     */
    private static PrivateKey loadPKCS8PrivateKey(byte[] keyBytes) throws Exception {
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 加载PKCS#1格式私钥并转换为PKCS#8
     */
    private static PrivateKey loadPKCS1PrivateKey(byte[] pkcs1Bytes) throws Exception {
        byte[] pkcs8Bytes = convertPKCS1ToPKCS8(pkcs1Bytes);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(pkcs8Bytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 将PKCS#1格式转换为PKCS#8格式
     */
    private static byte[] convertPKCS1ToPKCS8(byte[] pkcs1Bytes) {
        // RSA算法标识符
        byte[] rsaOID = {
            0x30, 0x0D, // SEQUENCE, length 13
            0x06, 0x09, 0x2A, (byte)0x86, 0x48, (byte)0x86, (byte)0xF7, 0x0D, 0x01, 0x01, 0x01, // RSA OID
            0x05, 0x00  // NULL
        };

        // 计算总长度
        int totalLength = 1 + 1 + 1 + rsaOID.length + getLengthBytes(pkcs1Bytes.length).length + pkcs1Bytes.length;
        byte[] lengthBytes = getLengthBytes(totalLength);
        
        // 构建PKCS#8结构
        byte[] pkcs8 = new byte[1 + lengthBytes.length + totalLength];
        int offset = 0;
        
        // SEQUENCE tag
        pkcs8[offset++] = 0x30;
        
        // Length
        System.arraycopy(lengthBytes, 0, pkcs8, offset, lengthBytes.length);
        offset += lengthBytes.length;
        
        // Version (INTEGER 0)
        pkcs8[offset++] = 0x02;
        pkcs8[offset++] = 0x01;
        pkcs8[offset++] = 0x00;
        
        // Algorithm identifier
        System.arraycopy(rsaOID, 0, pkcs8, offset, rsaOID.length);
        offset += rsaOID.length;
        
        // Private key (OCTET STRING)
        pkcs8[offset++] = 0x04;
        byte[] keyLengthBytes = getLengthBytes(pkcs1Bytes.length);
        System.arraycopy(keyLengthBytes, 0, pkcs8, offset, keyLengthBytes.length);
        offset += keyLengthBytes.length;
        System.arraycopy(pkcs1Bytes, 0, pkcs8, offset, pkcs1Bytes.length);
        
        return pkcs8;
    }

    /**
     * 获取ASN.1长度编码
     */
    private static byte[] getLengthBytes(int length) {
        if (length < 0x80) {
            return new byte[]{(byte) length};
        } else if (length < 0x100) {
            return new byte[]{(byte) 0x81, (byte) length};
        } else if (length < 0x10000) {
            return new byte[]{(byte) 0x82, (byte) (length >> 8), (byte) length};
        } else {
            return new byte[]{(byte) 0x83, (byte) (length >> 16), (byte) (length >> 8), (byte) length};
        }
    }

    /**
     * 检测私钥格式
     * 
     * @param privateKeyStr 私钥字符串
     * @return 格式类型：PKCS1 或 PKCS8
     */
    public static String detectPrivateKeyFormat(String privateKeyStr) {
        if (privateKeyStr.contains("-----BEGIN RSA PRIVATE KEY-----")) {
            return "PKCS1";
        } else if (privateKeyStr.contains("-----BEGIN PRIVATE KEY-----")) {
            return "PKCS8";
        } else {
            return "UNKNOWN";
        }
    }
}
