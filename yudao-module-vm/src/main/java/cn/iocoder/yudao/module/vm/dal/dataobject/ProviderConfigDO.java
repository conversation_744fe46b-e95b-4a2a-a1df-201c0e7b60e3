package cn.iocoder.yudao.module.vm.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 厂商配置 DO
 *
 * <AUTHOR>
 */
@TableName("vm_provider_config")
@KeySequence("vm_provider_config_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProviderConfigDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * 厂商类型：1-vmcardio，2-厂商B，3-厂商C
     */
    private Integer providerType;
    
    /**
     * 厂商名称
     */
    private String providerName;
    
    /**
     * API基础URL
     */
    private String baseUrl;
    
    /**
     * API Key
     */
    private String apiKey;
    
    /**
     * 公钥（用于加密请求）
     */
    private String publicKey;
    
    /**
     * 私钥（用于解密响应）
     */
    private String privateKey;
    
    /**
     * 是否启用：0-禁用，1-启用
     */
    private Boolean enabled;
    
    /**
     * 是否为默认厂商：0-否，1-是
     */
    private Boolean isDefault;
    
    /**
     * 超时时间（秒）
     */
    private Integer timeout;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * IP白名单（多个IP用逗号分隔）
     */
    private String ipWhitelist;
    
    /**
     * 扩展配置（JSON格式）
     */
    private String extConfig;
    
    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 令牌过期时间
     */
    private LocalDateTime tokenExpireTime;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 备注
     */
    private String remark;

}
