package cn.iocoder.yudao.module.vm.api.encryption;

import cn.iocoder.yudao.module.vm.enums.ProviderTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 加密策略工厂
 * 
 * 根据厂商类型获取对应的加密策略
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EncryptionStrategyFactory {

    private final ApplicationContext applicationContext;
    
    // 缓存加密策略实例
    private Map<Integer, EncryptionStrategy> strategyMap;

    @PostConstruct
    public void init() {
        strategyMap = new ConcurrentHashMap<>();
        
        // 获取所有加密策略实现
        Map<String, EncryptionStrategy> strategies = applicationContext.getBeansOfType(EncryptionStrategy.class);
        
        for (EncryptionStrategy strategy : strategies.values()) {
            // 根据加密类型映射到厂商类型
            Integer providerType = getProviderTypeByEncryptionType(strategy.getEncryptionType());
            if (providerType != null) {
                strategyMap.put(providerType, strategy);
                log.info("注册加密策略: {} -> {}", providerType, strategy.getEncryptionType());
            }
        }
    }

    /**
     * 根据厂商类型获取加密策略
     * 
     * @param providerType 厂商类型
     * @return 加密策略
     */
    public EncryptionStrategy getStrategy(Integer providerType) {
        EncryptionStrategy strategy = strategyMap.get(providerType);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的厂商加密类型: " + providerType);
        }
        return strategy;
    }

    /**
     * 根据加密类型获取厂商类型
     */
    private Integer getProviderTypeByEncryptionType(String encryptionType) {
        switch (encryptionType) {
            case "VMCARDIO_RSA":
                return ProviderTypeEnum.VMCARDIO.getType();
            case "LEMFT_AES":
                return ProviderTypeEnum.LEMFT.getType();
            default:
                log.warn("未知的加密类型: {}", encryptionType);
                return null;
        }
    }

}
