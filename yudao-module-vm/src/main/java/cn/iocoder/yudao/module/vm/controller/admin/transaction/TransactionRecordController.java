package cn.iocoder.yudao.module.vm.controller.admin.transaction;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
// import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
// import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.module.vm.controller.admin.transaction.vo.TransactionRecordPageReqVO;
import cn.iocoder.yudao.module.vm.controller.admin.transaction.vo.TransactionRecordRespVO;
import cn.iocoder.yudao.module.vm.dal.dataobject.TransactionRecordDO;
import cn.iocoder.yudao.module.vm.enums.ProviderTypeEnum;
import cn.iocoder.yudao.module.vm.enums.TransactionTypeEnum;
import cn.iocoder.yudao.module.vm.service.transaction.TransactionRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
// import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 交易记录")
@RestController
@RequestMapping("/vm/transaction-record")
@Validated
public class TransactionRecordController {

    @Resource
    private TransactionRecordService transactionRecordService;

    @GetMapping("/get")
    @Operation(summary = "获得交易记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('vm:transaction-record:query')")
    public CommonResult<TransactionRecordRespVO> getTransactionRecord(@RequestParam("id") Long id) {
        TransactionRecordDO transactionRecord = transactionRecordService.getTransactionRecord(id);
        TransactionRecordRespVO respVO = BeanUtils.toBean(transactionRecord, TransactionRecordRespVO.class);
        convertTransactionRecord(transactionRecord, respVO);
        return success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得交易记录分页")
    @PreAuthorize("@ss.hasPermission('vm:transaction-record:query')")
    public CommonResult<PageResult<TransactionRecordRespVO>> getTransactionRecordPage(@Valid TransactionRecordPageReqVO pageReqVO) {
        PageResult<TransactionRecordDO> pageResult = transactionRecordService.getTransactionRecordPage(pageReqVO);
        PageResult<TransactionRecordRespVO> result = new PageResult<>();
        result.setTotal(pageResult.getTotal());
        result.setList(pageResult.getList().stream().map(this::convertToRespVO).collect(java.util.stream.Collectors.toList()));
        return success(result);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出交易记录 Excel")
    @PreAuthorize("@ss.hasPermission('vm:transaction-record:export')")
    // @OperateLog(type = EXPORT)
    public void exportTransactionRecordExcel(@Valid TransactionRecordPageReqVO pageReqVO,
                                            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransactionRecordDO> list = transactionRecordService.getTransactionRecordPage(pageReqVO).getList();
        // 导出 Excel - 需要引入相关依赖
        // List<TransactionRecordRespVO> respList = list.stream().map(this::convertToRespVO).collect(java.util.stream.Collectors.toList());
        // ExcelUtils.write(response, "交易记录.xls", "数据", TransactionRecordRespVO.class, respList);

        // 临时实现：返回JSON格式数据
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("Excel导出功能需要引入相关依赖");
    }

    @GetMapping("/list-by-card")
    @Operation(summary = "根据卡片ID获取交易记录列表")
    @Parameter(name = "cardId", description = "卡片ID", required = true)
    @PreAuthorize("@ss.hasPermission('vm:transaction-record:query')")
    public CommonResult<List<TransactionRecordRespVO>> getTransactionRecordsByCardId(@RequestParam("cardId") Long cardId) {
        List<TransactionRecordDO> list = transactionRecordService.getTransactionRecordsByCardId(cardId);
        List<TransactionRecordRespVO> respList = list.stream().map(this::convertToRespVO).collect(java.util.stream.Collectors.toList());
        return success(respList);
    }

    @GetMapping("/list-by-third-party-card")
    @Operation(summary = "根据第三方卡片ID获取交易记录列表")
    @Parameter(name = "thirdPartyCardId", description = "第三方卡片ID", required = true)
    @PreAuthorize("@ss.hasPermission('vm:transaction-record:query')")
    public CommonResult<List<TransactionRecordRespVO>> getTransactionRecordsByThirdPartyCardId(@RequestParam("thirdPartyCardId") String thirdPartyCardId) {
        List<TransactionRecordDO> list = transactionRecordService.getTransactionRecordsByThirdPartyCardId(thirdPartyCardId);
        List<TransactionRecordRespVO> respList = list.stream().map(this::convertToRespVO).collect(java.util.stream.Collectors.toList());
        return success(respList);
    }

    /**
     * 转换交易记录信息，补充类型名称和状态名称
     */
    private void convertTransactionRecord(TransactionRecordDO source, TransactionRecordRespVO target) {
        // 设置交易类型名称
        TransactionTypeEnum typeEnum = TransactionTypeEnum.getByType(source.getTransactionType());
        if (typeEnum != null) {
            target.setTransactionTypeName(typeEnum.getName());
        }
        
        // 设置交易状态名称
        if (source.getStatus() != null) {
            switch (source.getStatus()) {
                case 1:
                    target.setStatusName("成功");
                    break;
                case 2:
                    target.setStatusName("失败");
                    break;
                case 3:
                    target.setStatusName("处理中");
                    break;
                default:
                    target.setStatusName("未知");
                    break;
            }
        }
        
        // 设置厂商名称
        ProviderTypeEnum providerEnum = ProviderTypeEnum.getByType(source.getProviderType());
        if (providerEnum != null) {
            target.setProviderName(providerEnum.getName());
        }
    }

    /**
     * 转换为响应VO
     */
    private TransactionRecordRespVO convertToRespVO(TransactionRecordDO source) {
        TransactionRecordRespVO target = BeanUtils.toBean(source, TransactionRecordRespVO.class);
        convertTransactionRecord(source, target);
        return target;
    }

}
