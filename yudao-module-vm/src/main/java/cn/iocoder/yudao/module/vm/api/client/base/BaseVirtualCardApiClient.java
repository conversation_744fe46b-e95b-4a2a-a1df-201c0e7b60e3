package cn.iocoder.yudao.module.vm.api.client.base;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 基础虚拟卡API客户端接口
 * 
 * 定义所有厂商都必须实现的核心接口
 */
public interface BaseVirtualCardApiClient {

    /**
     * 获取厂商类型
     *
     * @return 厂商类型
     */
    Integer getProviderType();

    /**
     * 获取访问令牌
     *
     * @return 访问令牌信息
     */
    TokenResult getAccessToken();

    /**
     * 检查令牌是否有效
     *
     * @return 是否有效
     */
    boolean isTokenValid();

    /**
     * 创建虚拟卡
     * 
     * @param request 创建卡片请求
     * @return 创建卡片响应
     */
    ApiResponseDTO<CreateCardResponseDTO> createCard(CreateCardRequestDTO request);

    /**
     * 获取卡片详情
     * 
     * @param request 卡片详情请求
     * @return 卡片详情响应
     */
    ApiResponseDTO<CardDetailResponseDTO> getCardDetail(CardDetailRequestDTO request);

    /**
     * 冻结卡片
     * 
     * @param cardId 卡片ID
     * @return 冻结结果
     */
    ApiResponseDTO<Void> freezeCard(String cardId);

    /**
     * 解冻卡片
     * 
     * @param cardId 卡片ID
     * @return 解冻结果
     */
    ApiResponseDTO<Void> unfreezeCard(String cardId);

    /**
     * 删除/销卡
     * 
     * @param cardId 卡片ID
     * @return 删除结果
     */
    ApiResponseDTO<Void> deleteCard(String cardId);

}
