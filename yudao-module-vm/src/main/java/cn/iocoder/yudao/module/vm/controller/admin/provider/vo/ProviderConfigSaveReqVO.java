package cn.iocoder.yudao.module.vm.controller.admin.provider.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 厂商配置保存 Request VO")
@Data
public class ProviderConfigSaveReqVO {

    @Schema(description = "主键ID", example = "1024")
    private Long id;

    @Schema(description = "厂商类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "厂商类型不能为空")
    private Integer providerType;

    @Schema(description = "厂商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "VMCardIO")
    @NotBlank(message = "厂商名称不能为空")
    private String providerName;

    @Schema(description = "API基础URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://sandbox-api.vmcardio.com/")
    @NotBlank(message = "API基础URL不能为空")
    private String baseUrl;

    @Schema(description = "API Key", requiredMode = Schema.RequiredMode.REQUIRED, example = "341916e58af445f8aadeb95******")
    @NotBlank(message = "API Key不能为空")
    private String apiKey;

    @Schema(description = "公钥（用于加密请求）")
    private String publicKey;

    @Schema(description = "私钥（用于解密响应）")
    private String privateKey;

    @Schema(description = "是否启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否启用不能为空")
    private Boolean enabled;

    @Schema(description = "是否为默认厂商", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否为默认厂商不能为空")
    private Boolean isDefault;

    @Schema(description = "超时时间（秒）", requiredMode = Schema.RequiredMode.REQUIRED, example = "30")
    @NotNull(message = "超时时间不能为空")
    private Integer timeout;

    @Schema(description = "重试次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    @NotNull(message = "重试次数不能为空")
    private Integer retryCount;

    @Schema(description = "IP白名单", example = "***********,***********")
    private String ipWhitelist;

    @Schema(description = "扩展配置", example = "{\"rsaType\":\"ECB_OAEP\",\"aesType\":\"GCM_NOPADDING\"}")
    private String extConfig;

    @Schema(description = "备注")
    private String remark;

}
