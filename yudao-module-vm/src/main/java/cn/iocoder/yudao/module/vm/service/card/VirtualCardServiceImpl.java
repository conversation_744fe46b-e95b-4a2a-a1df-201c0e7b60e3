package cn.iocoder.yudao.module.vm.service.card;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.vm.api.client.VirtualCardApiClient;
import cn.iocoder.yudao.module.vm.api.client.VirtualCardApiClientFactory;
import cn.iocoder.yudao.module.vm.api.dto.*;
import cn.iocoder.yudao.module.vm.controller.admin.card.vo.VirtualCardCreateReqVO;
import cn.iocoder.yudao.module.vm.controller.admin.card.vo.VirtualCardPageReqVO;
import cn.iocoder.yudao.module.vm.controller.admin.card.vo.VirtualCardUpdateReqVO;
import cn.iocoder.yudao.module.vm.dal.dataobject.ProviderConfigDO;
import cn.iocoder.yudao.module.vm.dal.dataobject.VirtualCardDO;
import cn.iocoder.yudao.module.vm.dal.mysql.VirtualCardMapper;
import cn.iocoder.yudao.module.vm.enums.ProviderTypeEnum;
import cn.iocoder.yudao.module.vm.enums.TransactionTypeEnum;
import cn.iocoder.yudao.module.vm.enums.VirtualCardStatusEnum;
import cn.iocoder.yudao.module.vm.service.provider.ProviderConfigService;
import cn.iocoder.yudao.module.vm.service.transaction.TransactionRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vm.enums.ErrorCodeConstants.*;

/**
 * 虚拟卡 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class VirtualCardServiceImpl implements VirtualCardService {

    @Resource
    private VirtualCardMapper virtualCardMapper;
    
    @Resource
    private ProviderConfigService providerConfigService;
    
    @Resource
    private TransactionRecordService transactionRecordService;

    @Resource
    private VirtualCardApiClientFactory apiClientFactory;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createVirtualCard(VirtualCardCreateReqVO createReqVO) {
        // 确定使用的厂商
        Integer providerType = createReqVO.getProviderType();
        if (providerType == null) {
            ProviderConfigDO defaultProvider = providerConfigService.getDefaultProvider();
            if (defaultProvider == null) {
                throw exception(PROVIDER_NOT_EXISTS, "未配置默认厂商");
            }
            providerType = defaultProvider.getProviderType();
        }
        
        // 获取API客户端
        VirtualCardApiClient apiClient = apiClientFactory.getApiClient(providerType);
        
        // 构建第三方API请求
        CreateCardRequestDTO apiRequest = buildCreateCardRequest(createReqVO);
        
        // 调用第三方API创建卡片
        ApiResponseDTO<CreateCardResponseDTO> apiResponse = apiClient.createCard(apiRequest);
        if (!apiResponse.isSuccess()) {
            throw exception(PROVIDER_API_ERROR, "创建卡片失败: " + apiResponse.getMsg());
        }
        
        // 保存本地记录
        VirtualCardDO virtualCard = buildVirtualCardDO(createReqVO, providerType, UUID.randomUUID().toString());
        virtualCardMapper.insert(virtualCard);
        
        // 记录交易
        transactionRecordService.recordTransaction(
                virtualCard.getId(),
                virtualCard.getThirdPartyCardId(),
                TransactionTypeEnum.CREATE_CARD.getType(),
                createReqVO.getAmount(),
                BigDecimal.ZERO,
                createReqVO.getAmount(),
                providerType,
                "创建虚拟卡",
                null
        );
        
        // 异步获取卡片详情并更新
        try {
            syncVirtualCard(virtualCard.getId());
        } catch (Exception e) {
            log.warn("同步卡片详情失败: {}", e.getMessage());
        }
        
        return virtualCard.getId();
    }

    @Override
    public void updateVirtualCard(VirtualCardUpdateReqVO updateReqVO) {
        // 校验存在
        validateVirtualCardExists(updateReqVO.getId());
        
        // 更新
        VirtualCardDO updateObj = BeanUtils.toBean(updateReqVO, VirtualCardDO.class);
        virtualCardMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVirtualCard(Long id) {
        // 校验存在
        VirtualCardDO virtualCard = validateVirtualCardExists(id);
        
        // 调用第三方API删除卡片
        VirtualCardApiClient apiClient = apiClientFactory.getApiClient(virtualCard.getProviderType());
        ApiResponseDTO<Void> apiResponse = apiClient.deleteCard(virtualCard.getThirdPartyCardId());
        if (!apiResponse.isSuccess()) {
            throw exception(PROVIDER_API_ERROR, "删除卡片失败: " + apiResponse.getMsg());
        }
        
        // 更新本地状态
        VirtualCardDO updateObj = new VirtualCardDO();
        updateObj.setId(id);
        updateObj.setStatus(VirtualCardStatusEnum.DELETED.getStatus());
        virtualCardMapper.updateById(updateObj);
        
        // 记录交易
        transactionRecordService.recordTransaction(
                virtualCard.getId(),
                virtualCard.getThirdPartyCardId(),
                TransactionTypeEnum.DELETE_CARD.getType(),
                BigDecimal.ZERO,
                virtualCard.getAvailableAmount(),
                BigDecimal.ZERO,
                virtualCard.getProviderType(),
                "删除虚拟卡",
                null
        );
    }

    @Override
    public VirtualCardDO getVirtualCard(Long id) {
        return virtualCardMapper.selectById(id);
    }

    @Override
    public PageResult<VirtualCardDO> getVirtualCardPage(VirtualCardPageReqVO pageReqVO) {
        return virtualCardMapper.selectPage(pageReqVO);
    }

    @Override
    public VirtualCardDO getByThirdPartyCardId(String thirdPartyCardId) {
        return virtualCardMapper.selectByThirdPartyCardId(thirdPartyCardId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void freezeVirtualCard(Long id) {
        // 校验存在
        VirtualCardDO virtualCard = validateVirtualCardExists(id);
        
        // 校验状态
        if (!VirtualCardStatusEnum.ACTIVE.getStatus().equals(virtualCard.getStatus())) {
            throw exception(VIRTUAL_CARD_STATUS_INVALID, "只有激活状态的卡片才能冻结");
        }
        
        // 调用第三方API冻结卡片
        VirtualCardApiClient apiClient = apiClientFactory.getApiClient(virtualCard.getProviderType());
        ApiResponseDTO<Void> apiResponse = apiClient.freezeCard(virtualCard.getThirdPartyCardId());
        if (!apiResponse.isSuccess()) {
            throw exception(PROVIDER_API_ERROR, "冻结卡片失败: " + apiResponse.getMsg());
        }
        
        // 更新本地状态
        VirtualCardDO updateObj = new VirtualCardDO();
        updateObj.setId(id);
        updateObj.setStatus(VirtualCardStatusEnum.FROZEN.getStatus());
        virtualCardMapper.updateById(updateObj);
        
        // 记录交易
        transactionRecordService.recordTransaction(
                virtualCard.getId(),
                virtualCard.getThirdPartyCardId(),
                TransactionTypeEnum.FREEZE.getType(),
                BigDecimal.ZERO,
                virtualCard.getAvailableAmount(),
                virtualCard.getAvailableAmount(),
                virtualCard.getProviderType(),
                "冻结虚拟卡",
                null
        );
    }

    /**
     * 校验虚拟卡是否存在
     */
    private VirtualCardDO validateVirtualCardExists(Long id) {
        VirtualCardDO virtualCard = virtualCardMapper.selectById(id);
        if (virtualCard == null) {
            throw exception(VIRTUAL_CARD_NOT_EXISTS);
        }
        return virtualCard;
    }



    /**
     * 构建创建卡片请求
     */
    private CreateCardRequestDTO buildCreateCardRequest(VirtualCardCreateReqVO createReqVO) {
        CreateCardRequestDTO request = new CreateCardRequestDTO();
        request.setProductCode(createReqVO.getProductCode());
        request.setFirstName(createReqVO.getFirstName());
        request.setLastName(createReqVO.getLastName());
        request.setAmount(createReqVO.getAmount());
        request.setLabel(createReqVO.getLabel());
        request.setAreaCode(createReqVO.getAreaCode());
        request.setMobile(createReqVO.getMobile());
        request.setEmail(createReqVO.getEmail());
        
        // 解析地址信息
        if (StrUtil.isNotBlank(createReqVO.getCardAddress())) {
            try {
                CardAddressDTO address = JSONUtil.toBean(createReqVO.getCardAddress(), CardAddressDTO.class);
                request.setCardAddress(address);
            } catch (Exception e) {
                log.warn("解析地址信息失败: {}", e.getMessage());
            }
        }
        
        return request;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unfreezeVirtualCard(Long id) {
        // 校验存在
        VirtualCardDO virtualCard = validateVirtualCardExists(id);

        // 校验状态
        if (!VirtualCardStatusEnum.FROZEN.getStatus().equals(virtualCard.getStatus())) {
            throw exception(VIRTUAL_CARD_STATUS_INVALID, "只有冻结状态的卡片才能解冻");
        }

        // 调用第三方API解冻卡片
        VirtualCardApiClient apiClient = apiClientFactory.getApiClient(virtualCard.getProviderType());
        ApiResponseDTO<Void> apiResponse = apiClient.unfreezeCard(virtualCard.getThirdPartyCardId());
        if (!apiResponse.isSuccess()) {
            throw exception(PROVIDER_API_ERROR, "解冻卡片失败: " + apiResponse.getMsg());
        }

        // 更新本地状态
        VirtualCardDO updateObj = new VirtualCardDO();
        updateObj.setId(id);
        updateObj.setStatus(VirtualCardStatusEnum.ACTIVE.getStatus());
        virtualCardMapper.updateById(updateObj);

        // 记录交易
        transactionRecordService.recordTransaction(
                virtualCard.getId(),
                virtualCard.getThirdPartyCardId(),
                TransactionTypeEnum.UNFREEZE.getType(),
                BigDecimal.ZERO,
                virtualCard.getAvailableAmount(),
                virtualCard.getAvailableAmount(),
                virtualCard.getProviderType(),
                "解冻虚拟卡",
                null
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rechargeVirtualCard(Long id, BigDecimal amount) {
        // 校验存在
        VirtualCardDO virtualCard = validateVirtualCardExists(id);

        // 校验状态
        if (!VirtualCardStatusEnum.ACTIVE.getStatus().equals(virtualCard.getStatus())) {
            throw exception(VIRTUAL_CARD_STATUS_INVALID, "只有激活状态的卡片才能充值");
        }

        // 校验金额
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw exception(TRANSACTION_AMOUNT_INVALID, "充值金额必须大于0");
        }

        // 调用第三方API充值
        VirtualCardApiClient apiClient = apiClientFactory.getApiClient(virtualCard.getProviderType());
        ApiResponseDTO<Void> apiResponse = apiClient.rechargeCard(virtualCard.getThirdPartyCardId(), amount);
        if (!apiResponse.isSuccess()) {
            throw exception(PROVIDER_API_ERROR, "充值失败: " + apiResponse.getMsg());
        }

        // 更新本地余额
        BigDecimal newBalance = virtualCard.getAvailableAmount().add(amount);
        VirtualCardDO updateObj = new VirtualCardDO();
        updateObj.setId(id);
        updateObj.setAvailableAmount(newBalance);
        updateObj.setLastSyncTime(LocalDateTime.now());
        virtualCardMapper.updateById(updateObj);

        // 记录交易
        transactionRecordService.recordTransaction(
                virtualCard.getId(),
                virtualCard.getThirdPartyCardId(),
                TransactionTypeEnum.RECHARGE.getType(),
                amount,
                virtualCard.getAvailableAmount(),
                newBalance,
                virtualCard.getProviderType(),
                "虚拟卡充值",
                null
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundVirtualCard(Long id, BigDecimal amount) {
        // 校验存在
        VirtualCardDO virtualCard = validateVirtualCardExists(id);

        // 校验状态
        if (!VirtualCardStatusEnum.ACTIVE.getStatus().equals(virtualCard.getStatus())) {
            throw exception(VIRTUAL_CARD_STATUS_INVALID, "只有激活状态的卡片才能退款");
        }

        // 校验金额
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw exception(TRANSACTION_AMOUNT_INVALID, "退款金额必须大于0");
        }
        if (amount.compareTo(virtualCard.getAvailableAmount()) > 0) {
            throw exception(VIRTUAL_CARD_BALANCE_INSUFFICIENT, "退款金额不能大于可用余额");
        }

        // 调用第三方API退款
        VirtualCardApiClient apiClient = apiClientFactory.getApiClient(virtualCard.getProviderType());
        ApiResponseDTO<Void> apiResponse = apiClient.refundCard(virtualCard.getThirdPartyCardId(), amount);
        if (!apiResponse.isSuccess()) {
            throw exception(PROVIDER_API_ERROR, "退款失败: " + apiResponse.getMsg());
        }

        // 更新本地余额
        BigDecimal newBalance = virtualCard.getAvailableAmount().subtract(amount);
        VirtualCardDO updateObj = new VirtualCardDO();
        updateObj.setId(id);
        updateObj.setAvailableAmount(newBalance);
        updateObj.setLastSyncTime(LocalDateTime.now());
        virtualCardMapper.updateById(updateObj);

        // 记录交易
        transactionRecordService.recordTransaction(
                virtualCard.getId(),
                virtualCard.getThirdPartyCardId(),
                TransactionTypeEnum.REFUND.getType(),
                amount,
                virtualCard.getAvailableAmount(),
                newBalance,
                virtualCard.getProviderType(),
                "虚拟卡退款",
                null
        );
    }

    @Override
    public void syncVirtualCard(Long id) {
        // 校验存在
        VirtualCardDO virtualCard = validateVirtualCardExists(id);

        try {
            // 调用第三方API获取卡片详情
            VirtualCardApiClient apiClient = apiClientFactory.getApiClient(virtualCard.getProviderType());
            CardDetailRequestDTO request = new CardDetailRequestDTO();
            request.setCardId(virtualCard.getThirdPartyCardId());

            ApiResponseDTO<CardDetailResponseDTO> apiResponse = apiClient.getCardDetail(request);
            if (!apiResponse.isSuccess()) {
                log.warn("同步卡片详情失败: {}", apiResponse.getMsg());
                return;
            }

            // 更新本地数据
            updateVirtualCardFromThirdParty(virtualCard, apiResponse.getData());

        } catch (Exception e) {
            log.error("同步卡片详情异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void batchSyncVirtualCards(Integer providerType) {
        List<VirtualCardDO> virtualCards;
        if (providerType != null) {
            virtualCards = virtualCardMapper.selectListByProviderType(providerType);
        } else {
            // 获取所有激活状态的卡片
            virtualCards = virtualCardMapper.selectListByStatus(VirtualCardStatusEnum.ACTIVE.getStatus());
        }

        for (VirtualCardDO virtualCard : virtualCards) {
            try {
                syncVirtualCard(virtualCard.getId());
                // 避免频繁调用API
                Thread.sleep(100);
            } catch (Exception e) {
                log.error("同步卡片{}失败: {}", virtualCard.getId(), e.getMessage());
            }
        }
    }

    @Override
    public List<VirtualCardDO> getVirtualCardsByProvider(Integer providerType) {
        return virtualCardMapper.selectListByProviderType(providerType);
    }

    @Override
    public List<CardProductResponseDTO> getProductCodes(Integer providerType) {
        // 确定使用的厂商
        if (providerType == null) {
            ProviderConfigDO defaultProvider = providerConfigService.getDefaultProvider();
            if (defaultProvider == null) {
                throw exception(PROVIDER_NOT_EXISTS, "未配置默认厂商");
            }
            providerType = defaultProvider.getProviderType();
        }

        // 获取API客户端
        VirtualCardApiClient apiClient = apiClientFactory.getApiClient(providerType);

        // 调用第三方API获取产品编码
        ApiResponseDTO<List<CardProductResponseDTO>> apiResponse = apiClient.getCardProducts();
        if (!apiResponse.isSuccess()) {
            throw exception(PROVIDER_API_ERROR, "获取产品编码失败: " + apiResponse.getMsg());
        }

        return apiResponse.getData();
    }

    /**
     * 构建虚拟卡DO对象
     */
    private VirtualCardDO buildVirtualCardDO(VirtualCardCreateReqVO createReqVO, Integer providerType, String thirdPartyCardId) {
        VirtualCardDO virtualCard = new VirtualCardDO();
        virtualCard.setThirdPartyCardId(thirdPartyCardId);
        virtualCard.setFirstName(createReqVO.getFirstName());
        virtualCard.setLastName(createReqVO.getLastName());
        virtualCard.setHolderName(createReqVO.getFirstName() + " " + createReqVO.getLastName());
        virtualCard.setAvailableAmount(createReqVO.getAmount());
        virtualCard.setStatus(VirtualCardStatusEnum.ACTIVE.getStatus());
        virtualCard.setProviderType(providerType);
        virtualCard.setProductCode(createReqVO.getProductCode());
        virtualCard.setLabel(createReqVO.getLabel());
        virtualCard.setAreaCode(createReqVO.getAreaCode());
        virtualCard.setMobile(createReqVO.getMobile());
        virtualCard.setEmail(createReqVO.getEmail());
        virtualCard.setCardAddress(createReqVO.getCardAddress());
        virtualCard.setRemark(createReqVO.getRemark());
        virtualCard.setLastSyncTime(LocalDateTime.now());

        return virtualCard;
    }

    /**
     * 从第三方数据更新本地虚拟卡信息
     */
    private void updateVirtualCardFromThirdParty(VirtualCardDO virtualCard, CardDetailResponseDTO thirdPartyData) {
        VirtualCardDO updateObj = new VirtualCardDO();
        updateObj.setId(virtualCard.getId());

        // 更新卡号信息
        if (StrUtil.isNotBlank(thirdPartyData.getCardNumber())) {
            updateObj.setCardNumber(thirdPartyData.getCardNumber());
        }
        if (StrUtil.isNotBlank(thirdPartyData.getCvv())) {
            updateObj.setCvv(thirdPartyData.getCvv());
        }
        if (StrUtil.isNotBlank(thirdPartyData.getExpire())) {
            updateObj.setExpireDate(thirdPartyData.getExpire());
        }

        // 更新状态
        if (StrUtil.isNotBlank(thirdPartyData.getStatus())) {
            VirtualCardStatusEnum statusEnum = VirtualCardStatusEnum.getByThirdPartyStatus(thirdPartyData.getStatus());
            if (statusEnum != null) {
                updateObj.setStatus(statusEnum.getStatus());
            }
        }

        // 更新余额
        if (thirdPartyData.getAvailableAmount() != null) {
            updateObj.setAvailableAmount(thirdPartyData.getAvailableAmount());
        }

        // 更新卡类型
        if (StrUtil.isNotBlank(thirdPartyData.getCardType())) {
            updateObj.setCardType(thirdPartyData.getCardType());
        }

        // 更新持卡人信息
        if (StrUtil.isNotBlank(thirdPartyData.getUserName())) {
            updateObj.setHolderName(thirdPartyData.getUserName());
        }

        // 更新第三方创建时间
        if (StrUtil.isNotBlank(thirdPartyData.getCreateTime())) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                updateObj.setThirdPartyCreateTime(LocalDateTime.parse(thirdPartyData.getCreateTime(), formatter));
            } catch (Exception e) {
                log.warn("解析第三方创建时间失败: {}", e.getMessage());
            }
        }

        // 更新地址信息
        if (thirdPartyData.getCardAddress() != null) {
            updateObj.setCardAddress(JSONUtil.toJsonStr(thirdPartyData.getCardAddress()));
        }

        updateObj.setLastSyncTime(LocalDateTime.now());
        virtualCardMapper.updateById(updateObj);
    }

}
