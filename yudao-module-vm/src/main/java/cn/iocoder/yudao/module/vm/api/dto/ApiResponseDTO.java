package cn.iocoder.yudao.module.vm.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API响应基础 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponseDTO<T> {

    /**
     * 响应码
     */
    @JsonProperty("code")
    private Integer code;

    /**
     * 响应消息
     */
    @JsonProperty("msg")
    private String msg;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    private T data;

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return code != null && (code == 0 || code == 200);
    }

    /**
     * 创建成功响应
     */
    public static <T> ApiResponseDTO<T> success(T data) {
        return new ApiResponseDTO<>(0, "成功", data);
    }

    /**
     * 创建成功响应（无数据）
     */
    public static <T> ApiResponseDTO<T> success() {
        return new ApiResponseDTO<>(0, "成功", null);
    }

    /**
     * 创建错误响应
     */
    public static <T> ApiResponseDTO<T> error(String message) {
        return new ApiResponseDTO<>(-1, message, null);
    }

    /**
     * 创建错误响应
     */
    public static <T> ApiResponseDTO<T> error(Integer code, String message) {
        return new ApiResponseDTO<>(code, message, null);
    }

}
