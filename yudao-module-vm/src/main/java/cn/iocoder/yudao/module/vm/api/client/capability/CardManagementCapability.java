package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.util.List;

/**
 * 卡片管理能力接口
 *
 * 支持卡片列表、详情、激活、冻结、解冻、销卡、交易列表等功能
 */
public interface CardManagementCapability {

    /**
     * 获取卡产品列表
     *
     * @return 卡产品列表响应
     */
    default ApiResponseDTO<List<CardProductResponseDTO>> getCardProducts() {
        throw new UnsupportedOperationException("该厂商不支持卡产品列表功能");
    }

    /**
     * 获取卡列表
     *
     * @param request 卡列表请求
     * @return 卡列表响应
     */
    default ApiResponseDTO<List<CardDetailResponseDTO>> getCardList(CardListRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持卡列表功能");
    }

    /**
     * 卡激活
     *
     * @param cardId 卡片ID
     * @return 激活结果
     */
    default ApiResponseDTO<Void> activateCard(String cardId) {
        throw new UnsupportedOperationException("该厂商不支持卡激活功能");
    }

    /**
     * 修改卡片限额
     *
     * @param request 修改限额请求
     * @return 修改结果
     */
    default ApiResponseDTO<Void> updateCardLimit(UpdateCardLimitRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持修改卡限额功能");
    }

    /**
     * 获取卡交易列表
     *
     * @param request 卡交易请求
     * @return 卡交易列表响应
     */
    default ApiResponseDTO<List<TransactionResponseDTO>> getCardTransactions(CardTransactionRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持卡交易列表功能");
    }

    /**
     * 商户交易评分
     *
     * @param request 交易评分请求
     * @return 评分结果
     */
    default ApiResponseDTO<MerchantScoreResponseDTO> getMerchantTransactionScore(MerchantScoreRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持商户交易评分功能");
    }

}
