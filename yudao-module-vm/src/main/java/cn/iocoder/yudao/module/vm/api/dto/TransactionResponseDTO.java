package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易记录响应DTO
 */
@Data
public class TransactionResponseDTO {

    /**
     * 第三方交易ID
     */
    private String thirdPartyTransactionId;

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 交易类型
     */
    private Integer transactionType;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易前余额
     */
    private BigDecimal balanceBefore;

    /**
     * 交易后余额
     */
    private BigDecimal balanceAfter;

    /**
     * 交易状态
     */
    private Integer status;

    /**
     * 交易描述
     */
    private String description;

    /**
     * 第三方交易时间
     */
    private LocalDateTime thirdPartyTransactionTime;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 扩展信息
     */
    private String extInfo;

}
