package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.util.List;

/**
 * 账户管理能力接口
 * 
 * 支持账户余额、钱包管理等功能
 */
public interface AccountCapability {

    /**
     * 获取账户余额
     * 
     * @return 账户余额响应
     */
    ApiResponseDTO<AccountBalanceResponseDTO> getAccountBalance();

    /**
     * 获取钱包列表
     * 
     * @return 钱包列表响应
     */
    default ApiResponseDTO<List<WalletResponseDTO>> getWalletList() {
        throw new UnsupportedOperationException("该厂商不支持钱包列表功能");
    }

    /**
     * 获取钱包详情
     * 
     * @param walletId 钱包ID
     * @return 钱包详情响应
     */
    default ApiResponseDTO<WalletResponseDTO> getWalletDetail(String walletId) {
        throw new UnsupportedOperationException("该厂商不支持钱包详情功能");
    }

}
