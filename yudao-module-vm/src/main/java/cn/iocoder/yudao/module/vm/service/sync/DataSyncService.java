package cn.iocoder.yudao.module.vm.service.sync;

/**
 * 数据同步 Service 接口
 *
 * <AUTHOR>
 */
public interface DataSyncService {

    /**
     * 同步所有虚拟卡数据
     */
    void syncAllVirtualCards();

    /**
     * 同步指定厂商的虚拟卡数据
     *
     * @param providerType 厂商类型
     */
    void syncVirtualCardsByProvider(Integer providerType);

    /**
     * 同步单个虚拟卡数据
     *
     * @param cardId 虚拟卡ID
     */
    void syncVirtualCard(Long cardId);

    /**
     * 检查数据一致性
     *
     * @return 检查结果报告
     */
    String checkDataConsistency();

    /**
     * 修复数据不一致问题
     *
     * @param autoFix 是否自动修复
     * @return 修复结果报告
     */
    String fixDataInconsistency(boolean autoFix);

}
