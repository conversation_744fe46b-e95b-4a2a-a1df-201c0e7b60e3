package cn.iocoder.yudao.module.vm.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 创建卡片请求 DTO
 */
@Data
public class CreateCardRequestDTO {

    /**
     * 持卡人ID（某些厂商需要）
     */
    @JsonProperty("cardholder_id")
    private String cardholderId;

    /**
     * 产品编码
     */
    @JsonProperty("product_code")
    private String productCode;

    /**
     * 持卡人名
     */
    @JsonProperty("first_name")
    private String firstName;

    /**
     * 持卡人姓
     */
    @JsonProperty("last_name")
    private String lastName;

    /**
     * 卡内充值金额
     */
    @JsonProperty("amount")
    private BigDecimal amount;

    /**
     * 卡标签
     */
    @JsonProperty("label")
    private String label;

    /**
     * 区号
     */
    @JsonProperty("area_code")
    private String areaCode;

    /**
     * 手机号
     */
    @JsonProperty("mobile")
    private String mobile;

    /**
     * 邮箱
     */
    @JsonProperty("email")
    private String email;

    /**
     * 地址信息
     */
    @JsonProperty("card_address")
    private CardAddressDTO cardAddress;

}
