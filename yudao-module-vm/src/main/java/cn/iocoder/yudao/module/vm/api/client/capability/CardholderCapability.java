package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.util.List;

/**
 * 持卡人管理能力接口
 * 
 * 支持持卡人的创建、编辑、删除等功能
 */
public interface CardholderCapability {

    /**
     * 获取持卡人列表
     * 
     * @param request 持卡人列表请求
     * @return 持卡人列表响应
     */
    default ApiResponseDTO<List<CardholderResponseDTO>> getCardholderList(CardholderListRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持持卡人列表功能");
    }

    /**
     * 创建持卡人
     * 
     * @param request 创建持卡人请求
     * @return 创建持卡人响应
     */
    default ApiResponseDTO<CardholderResponseDTO> createCardholder(CreateCardholderRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持创建持卡人功能");
    }

    /**
     * 编辑持卡人
     * 
     * @param request 编辑持卡人请求
     * @return 编辑结果
     */
    default ApiResponseDTO<Void> updateCardholder(UpdateCardholderRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持编辑持卡人功能");
    }

    /**
     * 获取持卡人详情
     * 
     * @param cardholderId 持卡人ID
     * @return 持卡人详情响应
     */
    default ApiResponseDTO<CardholderResponseDTO> getCardholderDetail(String cardholderId) {
        throw new UnsupportedOperationException("该厂商不支持持卡人详情功能");
    }

    /**
     * 删除持卡人
     * 
     * @param cardholderId 持卡人ID
     * @return 删除结果
     */
    default ApiResponseDTO<Void> deleteCardholder(String cardholderId) {
        throw new UnsupportedOperationException("该厂商不支持删除持卡人功能");
    }

}
