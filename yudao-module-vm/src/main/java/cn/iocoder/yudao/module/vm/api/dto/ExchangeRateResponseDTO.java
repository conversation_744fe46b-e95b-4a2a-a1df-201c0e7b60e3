package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 汇率查询响应DTO
 */
@Data
public class ExchangeRateResponseDTO {

    /**
     * 源币种
     */
    private String fromCurrency;

    /**
     * 目标币种
     */
    private String toCurrency;

    /**
     * 汇率
     */
    private BigDecimal rate;

    /**
     * 源金额
     */
    private BigDecimal fromAmount;

    /**
     * 目标金额
     */
    private BigDecimal toAmount;

    /**
     * 汇率更新时间
     */
    private String updateTime;

}
