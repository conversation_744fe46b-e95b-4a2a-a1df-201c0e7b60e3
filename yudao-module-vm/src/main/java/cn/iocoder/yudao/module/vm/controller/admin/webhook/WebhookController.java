package cn.iocoder.yudao.module.vm.controller.admin.webhook;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
// import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.vm.dal.dataobject.VirtualCardDO;
import cn.iocoder.yudao.module.vm.service.card.VirtualCardService;
import cn.iocoder.yudao.module.vm.service.transaction.TransactionRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - Webhook回调")
@RestController
@RequestMapping("/vm/webhook")
@Validated
@RequiredArgsConstructor
@Slf4j
public class WebhookController {

    private final VirtualCardService virtualCardService;
    private final TransactionRecordService transactionRecordService;

    @PostMapping("/vmcardio")
    @Operation(summary = "VMCardIO Webhook回调")
    @PermitAll
    public CommonResult<String> handleVmCardIoWebhook(@RequestBody String payload,
                                                      HttpServletRequest request) {
        log.info("收到VMCardIO Webhook回调: {}", payload);
        
        try {
            // 记录请求头信息
            String userAgent = request.getHeader("User-Agent");
            String signature = request.getHeader("X-Signature");
            String timestamp = request.getHeader("X-Timestamp");
            
            log.info("Webhook请求头 - User-Agent: {}, Signature: {}, Timestamp: {}", 
                    userAgent, signature, timestamp);
            
            // 解析Webhook数据
            Map<String, Object> webhookData = JSONUtil.toBean(payload, Map.class);
            String eventType = (String) webhookData.get("event_type");
            Map<String, Object> data = (Map<String, Object>) webhookData.get("data");
            
            if (StrUtil.isBlank(eventType) || data == null) {
                log.warn("Webhook数据格式不正确: {}", payload);
                return success("数据格式不正确");
            }
            
            // 处理不同类型的事件
            switch (eventType) {
                case "card.created":
                    handleCardCreated(data);
                    break;
                case "card.updated":
                    handleCardUpdated(data);
                    break;
                case "card.deleted":
                    handleCardDeleted(data);
                    break;
                case "card.frozen":
                    handleCardFrozen(data);
                    break;
                case "card.unfrozen":
                    handleCardUnfrozen(data);
                    break;
                case "transaction.completed":
                    handleTransactionCompleted(data);
                    break;
                case "transaction.failed":
                    handleTransactionFailed(data);
                    break;
                default:
                    log.warn("未知的事件类型: {}", eventType);
                    break;
            }
            
            return success("处理成功");
            
        } catch (Exception e) {
            log.error("处理VMCardIO Webhook失败", e);
            return success("处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理卡片创建事件
     */
    private void handleCardCreated(Map<String, Object> data) {
        try {
            String cardId = (String) data.get("card_id");
            if (StrUtil.isNotBlank(cardId)) {
                VirtualCardDO virtualCard = virtualCardService.getByThirdPartyCardId(cardId);
                if (virtualCard != null) {
                    // 同步卡片信息
                    virtualCardService.syncVirtualCard(virtualCard.getId());
                    log.info("已同步创建的卡片: {}", cardId);
                }
            }
        } catch (Exception e) {
            log.error("处理卡片创建事件失败", e);
        }
    }

    /**
     * 处理卡片更新事件
     */
    private void handleCardUpdated(Map<String, Object> data) {
        try {
            String cardId = (String) data.get("card_id");
            if (StrUtil.isNotBlank(cardId)) {
                VirtualCardDO virtualCard = virtualCardService.getByThirdPartyCardId(cardId);
                if (virtualCard != null) {
                    // 同步卡片信息
                    virtualCardService.syncVirtualCard(virtualCard.getId());
                    log.info("已同步更新的卡片: {}", cardId);
                }
            }
        } catch (Exception e) {
            log.error("处理卡片更新事件失败", e);
        }
    }

    /**
     * 处理卡片删除事件
     */
    private void handleCardDeleted(Map<String, Object> data) {
        try {
            String cardId = (String) data.get("card_id");
            if (StrUtil.isNotBlank(cardId)) {
                VirtualCardDO virtualCard = virtualCardService.getByThirdPartyCardId(cardId);
                if (virtualCard != null) {
                    // 同步卡片信息
                    virtualCardService.syncVirtualCard(virtualCard.getId());
                    log.info("已同步删除的卡片: {}", cardId);
                }
            }
        } catch (Exception e) {
            log.error("处理卡片删除事件失败", e);
        }
    }

    /**
     * 处理卡片冻结事件
     */
    private void handleCardFrozen(Map<String, Object> data) {
        try {
            String cardId = (String) data.get("card_id");
            if (StrUtil.isNotBlank(cardId)) {
                VirtualCardDO virtualCard = virtualCardService.getByThirdPartyCardId(cardId);
                if (virtualCard != null) {
                    // 同步卡片信息
                    virtualCardService.syncVirtualCard(virtualCard.getId());
                    log.info("已同步冻结的卡片: {}", cardId);
                }
            }
        } catch (Exception e) {
            log.error("处理卡片冻结事件失败", e);
        }
    }

    /**
     * 处理卡片解冻事件
     */
    private void handleCardUnfrozen(Map<String, Object> data) {
        try {
            String cardId = (String) data.get("card_id");
            if (StrUtil.isNotBlank(cardId)) {
                VirtualCardDO virtualCard = virtualCardService.getByThirdPartyCardId(cardId);
                if (virtualCard != null) {
                    // 同步卡片信息
                    virtualCardService.syncVirtualCard(virtualCard.getId());
                    log.info("已同步解冻的卡片: {}", cardId);
                }
            }
        } catch (Exception e) {
            log.error("处理卡片解冻事件失败", e);
        }
    }

    /**
     * 处理交易完成事件
     */
    private void handleTransactionCompleted(Map<String, Object> data) {
        try {
            String cardId = (String) data.get("card_id");
            String transactionId = (String) data.get("transaction_id");
            
            if (StrUtil.isNotBlank(cardId)) {
                VirtualCardDO virtualCard = virtualCardService.getByThirdPartyCardId(cardId);
                if (virtualCard != null) {
                    // 同步卡片信息（更新余额等）
                    virtualCardService.syncVirtualCard(virtualCard.getId());
                    log.info("已同步交易完成的卡片: {}, 交易ID: {}", cardId, transactionId);
                }
            }
        } catch (Exception e) {
            log.error("处理交易完成事件失败", e);
        }
    }

    /**
     * 处理交易失败事件
     */
    private void handleTransactionFailed(Map<String, Object> data) {
        try {
            String cardId = (String) data.get("card_id");
            String transactionId = (String) data.get("transaction_id");
            String failureReason = (String) data.get("failure_reason");
            
            if (StrUtil.isNotBlank(cardId)) {
                VirtualCardDO virtualCard = virtualCardService.getByThirdPartyCardId(cardId);
                if (virtualCard != null) {
                    // 同步卡片信息
                    virtualCardService.syncVirtualCard(virtualCard.getId());
                    log.info("已同步交易失败的卡片: {}, 交易ID: {}, 失败原因: {}", 
                            cardId, transactionId, failureReason);
                }
            }
        } catch (Exception e) {
            log.error("处理交易失败事件失败", e);
        }
    }

    @GetMapping("/test")
    @Operation(summary = "Webhook测试接口")
    @PermitAll
    public CommonResult<String> testWebhook() {
        return success("Webhook服务正常运行");
    }

}
