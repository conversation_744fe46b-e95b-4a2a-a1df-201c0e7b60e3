package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 交易记录请求DTO
 */
@Data
public class TransactionRequestDTO {

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 交易类型
     */
    private Integer transactionType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;

}
