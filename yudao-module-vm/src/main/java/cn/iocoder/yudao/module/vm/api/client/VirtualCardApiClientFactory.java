package cn.iocoder.yudao.module.vm.api.client;

import cn.iocoder.yudao.module.vm.dal.dataobject.ProviderConfigDO;
import cn.iocoder.yudao.module.vm.service.provider.ProviderConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vm.enums.ErrorCodeConstants.*;

/**
 * 虚拟卡API客户端工厂
 * 
 * 负责管理和提供不同厂商的API客户端实例
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class VirtualCardApiClientFactory {

    private final ApplicationContext applicationContext;
    private final ProviderConfigService providerConfigService;
    
    // 缓存API客户端实例
    private Map<Integer, VirtualCardApiClient> apiClientMap;

    @PostConstruct
    public void init() {
        // 初始化API客户端映射
        refreshApiClients();
    }

    /**
     * 根据厂商类型获取API客户端
     * 
     * @param providerType 厂商类型
     * @return API客户端
     */
    public VirtualCardApiClient getApiClient(Integer providerType) {
        if (apiClientMap == null) {
            refreshApiClients();
        }
        
        VirtualCardApiClient apiClient = apiClientMap.get(providerType);
        if (apiClient == null) {
            throw exception(PROVIDER_NOT_EXISTS, "不支持的厂商类型: " + providerType);
        }
        
        // 检查厂商配置是否启用
        ProviderConfigDO config = providerConfigService.getByProviderType(providerType);
        if (config == null || !config.getEnabled()) {
            throw exception(PROVIDER_DISABLED, "厂商已禁用: " + providerType);
        }
        
        return apiClient;
    }

    /**
     * 获取默认API客户端
     * 
     * @return 默认API客户端
     */
    public VirtualCardApiClient getDefaultApiClient() {
        ProviderConfigDO defaultProvider = providerConfigService.getDefaultProvider();
        if (defaultProvider == null) {
            throw exception(PROVIDER_NOT_EXISTS, "未配置默认厂商");
        }
        
        return getApiClient(defaultProvider.getProviderType());
    }

    /**
     * 刷新API客户端映射
     */
    public void refreshApiClients() {
        try {
            apiClientMap = applicationContext.getBeansOfType(VirtualCardApiClient.class)
                    .values()
                    .stream()
                    .collect(Collectors.toConcurrentMap(
                            VirtualCardApiClient::getProviderType,
                            client -> client,
                            (existing, replacement) -> replacement,
                            ConcurrentHashMap::new
                    ));

            // 刷新所有客户端的配置缓存
            refreshAllClientConfigs();

            log.info("已加载 {} 个API客户端: {}",
                    apiClientMap.size(),
                    apiClientMap.keySet());

        } catch (Exception e) {
            log.error("刷新API客户端失败", e);
            throw new RuntimeException("刷新API客户端失败", e);
        }
    }

    /**
     * 刷新所有客户端的配置缓存
     */
    private void refreshAllClientConfigs() {
        if (apiClientMap != null) {
            apiClientMap.values().forEach(client -> {
                try {
                    // 如果客户端实现了ConfigurableApiClient接口，调用刷新方法
                    if (client instanceof ConfigurableApiClient) {
                        ((ConfigurableApiClient) client).refreshConfig();
                    }
                } catch (Exception e) {
                    log.warn("刷新客户端配置失败: {}", client.getProviderType(), e);
                }
            });
        }
    }

    /**
     * 检查厂商是否支持
     * 
     * @param providerType 厂商类型
     * @return 是否支持
     */
    public boolean isProviderSupported(Integer providerType) {
        if (apiClientMap == null) {
            refreshApiClients();
        }
        return apiClientMap.containsKey(providerType);
    }

    /**
     * 获取所有支持的厂商类型
     * 
     * @return 支持的厂商类型列表
     */
    public java.util.Set<Integer> getSupportedProviderTypes() {
        if (apiClientMap == null) {
            refreshApiClients();
        }
        return apiClientMap.keySet();
    }

}
