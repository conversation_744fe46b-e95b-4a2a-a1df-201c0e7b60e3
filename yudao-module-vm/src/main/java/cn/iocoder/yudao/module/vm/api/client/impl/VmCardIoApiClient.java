package cn.iocoder.yudao.module.vm.api.client.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.vm.api.client.ConfigurableApiClient;
import cn.iocoder.yudao.module.vm.api.client.VirtualCardApiClient;
import cn.iocoder.yudao.module.vm.api.dto.*;
import cn.iocoder.yudao.module.vm.api.encryption.EncryptionStrategyFactory;
import cn.iocoder.yudao.module.vm.dal.dataobject.ProviderConfigDO;
import cn.iocoder.yudao.module.vm.enums.ProviderTypeEnum;
import cn.iocoder.yudao.module.vm.service.provider.ProviderConfigService;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vm.enums.ErrorCodeConstants.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vm.enums.ErrorCodeConstants.*;

/**
 * VMCardIO API客户端实现
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class VmCardIoApiClient implements VirtualCardApiClient, ConfigurableApiClient {

    private final ProviderConfigService providerConfigService;
    private final EncryptionStrategyFactory encryptionStrategyFactory;

    // 缓存的厂商配置
    private volatile ProviderConfigDO cachedConfig;
    private volatile long lastConfigUpdateTime = 0;

    @Override
    public ApiResponseDTO<AccountBalanceResponseDTO> getAccountBalance() {
        try {
            ProviderConfigDO config = getProviderConfig();

            // 发送请求
            String responseBody = sendRequest(config, "/getAccountBalance", new HashMap<>());

            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);

        } catch (Exception e) {
            log.error("获取账户余额失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<List<CardProductResponseDTO>> getCardProducts() {
        try {
            ProviderConfigDO config = getProviderConfig();

            // 发送请求
            String responseBody = sendRequest(config, "/getCardProducts", new HashMap<>());

            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);

        } catch (Exception e) {
            log.error("获取卡产品列表失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<CreateCardResponseDTO> createCard(CreateCardRequestDTO request) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("product_code", request.getProductCode());
            bizContent.put("first_name", request.getFirstName());
            bizContent.put("last_name", request.getLastName());
            bizContent.put("amount", request.getAmount());
            if (StrUtil.isNotBlank(request.getLabel())) {
                bizContent.put("label", request.getLabel());
            }
            if (StrUtil.isNotBlank(request.getAreaCode())) {
                bizContent.put("area_code", request.getAreaCode());
            }
            if (StrUtil.isNotBlank(request.getMobile())) {
                bizContent.put("mobile", request.getMobile());
            }
            if (StrUtil.isNotBlank(request.getEmail())) {
                bizContent.put("email", request.getEmail());
            }
            if (request.getCardAddress() != null) {
                bizContent.put("card_address", request.getCardAddress());
            }

            // 发送请求
            String responseBody = sendRequest(config, "/createCard", bizContent);

            try {
                // 解析响应
                return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            } catch (JSONException e) {
                return new ApiResponseDTO(0, "成功", "");
            }
            
        } catch (Exception e) {
            log.error("创建虚拟卡失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<CardDetailResponseDTO> getCardDetail(CardDetailRequestDTO request) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", request.getCardId());

            // 发送请求
            String responseBody = sendRequest(config, "/cardDetail", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("获取卡片详情失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<Void> deleteCard(String cardId) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", cardId);

            // 发送请求
            String responseBody = sendRequest(config, "/deleteCard", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("删除卡片失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<Void> freezeCard(String cardId) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", cardId);
            bizContent.put("action", "freeze");

            // 发送请求
            String responseBody = sendRequest(config, "/freezeCard", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("冻结卡片失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<Void> unfreezeCard(String cardId) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", cardId);
            bizContent.put("action", "unfreeze");

            // 发送请求
            String responseBody = sendRequest(config, "/freezeCard", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("解冻卡片失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<Void> rechargeCard(String cardId, BigDecimal amount) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", cardId);
            bizContent.put("amount", amount);

            // 发送请求
            String responseBody = sendRequest(config, "/rechargeCard", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("卡片充值失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public ApiResponseDTO<Void> refundCard(String cardId, BigDecimal amount) {
        try {
            ProviderConfigDO config = getProviderConfig();
            
            // 构建请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("card_id", cardId);
            bizContent.put("amount", amount);

            // 发送请求
            String responseBody = sendRequest(config, "/refundCard", bizContent);
            
            // 解析响应
            return JSONUtil.toBean(responseBody, ApiResponseDTO.class);
            
        } catch (Exception e) {
            log.error("卡片退款失败", e);
            throw exception(PROVIDER_API_ERROR, e.getMessage());
        }
    }

    @Override
    public Integer getProviderType() {
        return ProviderTypeEnum.VMCARDIO.getType();
    }

    @Override
    public TokenResult getAccessToken() {
        try {
            ProviderConfigDO config = getProviderConfig();

            // 构建获取token的请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("app_id", config.getApiKey());
            requestData.put("app_secret", config.getPrivateKey());


            // 发送获取token请求
            String url = config.getBaseUrl() + "/getAccessToken?app_id=" + config.getApiKey() + "&app_secret=" + config.getPrivateKey();
            HttpResponse response = HttpRequest.get(url)
                    .header("Content-Type", "application/json")
                    .timeout(config.getTimeout() * 1000)
                    .execute();

            if (!response.isOk()) {
                return TokenResult.failure("HTTP请求失败: " + response.getStatus());
            }

            // 解析token响应
            String responseBody = response.body();
            Map<String, Object> tokenResponse = JSONUtil.toBean(responseBody, Map.class);

            if (Objects.equals(tokenResponse.get("code"), 0)) {
                Map<String, Object> data = (Map<String, Object>) tokenResponse.get("data");
                String accessToken = (String) data.get("token");
                Integer expiredTime = (Integer)data.get("expired_time");

                // 保存token到数据库
                saveTokenToConfig(config, accessToken, null, expiredTime);

                return TokenResult.success(accessToken, "Bearer", LocalDateTimeUtil.of(expiredTime), null);
            } else {
                return TokenResult.failure((String) tokenResponse.get("message"));
            }

        } catch (Exception e) {
            log.error("获取访问令牌失败", e);
            return TokenResult.failure("获取访问令牌失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isTokenValid() {
        try {
            ProviderConfigDO config = getProviderConfig();

            // 检查token是否存在且未过期
            if (StrUtil.isBlank(config.getAccessToken())) {
                return false;
            }

            if (config.getTokenExpireTime() != null &&
                LocalDateTime.now().isAfter(config.getTokenExpireTime().minusMinutes(5L))) {
                // 提前5分钟认为token过期，避免请求时才发现过期
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("检查令牌有效性失败", e);
            return false;
        }
    }

    /**
     * 获取厂商配置（带缓存）
     */
    private ProviderConfigDO getProviderConfig() {
        long currentTime = System.currentTimeMillis();

        // 如果缓存为空或者超过5分钟，重新加载配置
        if (cachedConfig == null || (currentTime - lastConfigUpdateTime) > 5 * 60 * 1000) {
            synchronized (this) {
                // 双重检查锁定
                if (cachedConfig == null || (currentTime - lastConfigUpdateTime) > 5 * 60 * 1000) {
                    ProviderConfigDO config = providerConfigService.getByProviderType(ProviderTypeEnum.VMCARDIO.getType());
                    if (config == null || !config.getEnabled()) {
                        throw exception(PROVIDER_DISABLED);
                    }
                    cachedConfig = config;
                    lastConfigUpdateTime = currentTime;
                    log.debug("已刷新厂商配置缓存: {}", config.getProviderName());
                }
            }
        }

        return cachedConfig;
    }

    /**
     * 强制刷新配置缓存
     */
    public void refreshConfig() {
        synchronized (this) {
            cachedConfig = null;
            lastConfigUpdateTime = 0;
        }
        // 重新加载配置
        getProviderConfig();
    }

    /**
     * 保存token到配置
     */
    private void saveTokenToConfig(ProviderConfigDO config, String accessToken, String refreshToken, Integer expiresIn) {
        config.setAccessToken(accessToken);
        config.setRefreshToken(refreshToken);
        if (expiresIn != null) {
            config.setTokenExpireTime(LocalDateTimeUtil.of(expiresIn));
        }
        // 更新数据库中的token信息
        providerConfigService.updateProviderToken(config.getId(),
            accessToken, refreshToken, config.getTokenExpireTime());
    }

    /**
     * 确保token有效
     */
    private void ensureTokenValid(ProviderConfigDO config) {
        if (!isTokenValid()) {
            // 没有刷新token，重新获取
            TokenResult newTokenResult = getAccessToken();
            if (!newTokenResult.isSuccess()) {
                throw new RuntimeException("无法获取有效的访问令牌: " + newTokenResult.getErrorMessage());
            }
        }
    }

    /**
     * 发送请求
     */
    private String sendRequest(ProviderConfigDO config, String endpoint, Map<String, Object> bizContent) {
        try {
            // 确保token有效
            ensureTokenValid(config);

            // 使用加密策略对bizContent进行加密
            String encryptedContent = encryptionStrategyFactory.getStrategy(getProviderType())
                    .encrypt(JSONUtil.toJsonStr(bizContent), config.getPublicKey());

            // 发送HTTP请求，使用access token
            String url = config.getBaseUrl() + endpoint;
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .header("Authorization", config.getAccessToken())
                    .body(encryptedContent)
                    .timeout(config.getTimeout() * 1000)
                    .execute();

            if (!response.isOk()) {
                throw new RuntimeException("HTTP请求失败: " + response.getStatus());
            }

            // 解析响应
            String responseBody = response.body();
            ApiResponseDTO<?> apiResponse = JSONUtil.toBean(responseBody, ApiResponseDTO.class);

            if (apiResponse.getCode() == 0) {
                // 如果需要解密响应数据，可以在这里处理
                try {
                    return JSONUtil.toJsonStr(apiResponse.getData());
                } catch (Exception e) {
                    return apiResponse.getData().toString();
                }
            } else {
                throw new RuntimeException("API调用失败: " + apiResponse.getMsg());
            }

        } catch (Exception e) {
            log.error("发送请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("发送请求失败: " + e.getMessage(), e);
        }
    }

}
