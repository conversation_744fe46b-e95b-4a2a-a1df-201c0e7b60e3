package cn.iocoder.yudao.module.vm.api.encryption.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.vm.api.encryption.EncryptionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * VMCardIO加密策略实现
 * 
 * 使用RSA加密，PKCS1Padding填充，Base64+十六进制编码
 */
@Component
@Slf4j
public class VmCardIoEncryptionStrategy implements EncryptionStrategy {

    private static final String RSA_ALGORITHM = "RSA";
    private static final String RSA_TRANSFORMATION = "RSA/ECB/PKCS1Padding";
    private static final String ENCRYPTION_TYPE = "VMCARDIO_RSA";

    @Override
    public String encrypt(String data, String publicKey) {
        try {
            // 1. 加载公钥
            PublicKey key = loadPublicKey(publicKey);
            
            // 2. 使用RSA公钥分块加密JSON数据
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            
            // RSA加密的最大数据长度（2048位密钥使用PKCS1Padding时为245字节）
            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
            byte[] encryptedData;
            
            if (dataBytes.length <= 245) {
                // 数据长度在限制内，直接加密
                encryptedData = cipher.doFinal(dataBytes);
            } else {
                // 数据过长，使用分块加密
                encryptedData = encryptByBlocks(cipher, dataBytes, 245);
            }
            
            // 3. Base64编码
            String base64EncodedData = Base64.encode(encryptedData);
            
            // 4. 十六进制编码
            String hexEncodedData = HexUtil.encodeHexStr(base64EncodedData.getBytes(StandardCharsets.UTF_8));

            // 5. 构建返回结果
            Map<String, String> result = new HashMap<>();
            result.put("content", hexEncodedData);

            return JSONUtil.toJsonPrettyStr(result);

        } catch (Exception e) {
            log.error("VMCardIO加密失败", e);
            Map<String, String> errorResult = new HashMap<>();
            errorResult.put("error", "加密失败: " + e.getMessage());
            return JSONUtil.toJsonPrettyStr(errorResult);
        }
    }

    @Override
    public String decrypt(String encryptedData, String privateKey) {
        try {
            // 1. 十六进制解码
            byte[] hexDecodedData = HexUtil.decodeHex(encryptedData);
            String base64EncodedData = new String(hexDecodedData, StandardCharsets.UTF_8);
            
            // 2. Base64解码
            byte[] base64DecodedData = Base64.decode(base64EncodedData);
            
            // 3. 使用RSA私钥解密
            PrivateKey key = loadPrivateKey(privateKey);
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, key);
            
            // 分块解密数据（RSA解密块大小为256字节，对应2048位密钥）
            byte[] decryptedData = decryptByBlocks(cipher, base64DecodedData, 256);
            
            // 4. 转换为JSON字符串
            return new String(decryptedData, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            log.error("VMCardIO解密失败", e);
            return "解密失败: " + e.getMessage();
        }
    }

    @Override
    public String getEncryptionType() {
        return ENCRYPTION_TYPE;
    }

    /**
     * RSA分块加密
     */
    private byte[] encryptByBlocks(Cipher cipher, byte[] data, int maxBlockSize) throws Exception {
        int inputLen = data.length;
        int offset = 0;
        byte[] cache;
        int i = 0;
        
        java.io.ByteArrayOutputStream out = new java.io.ByteArrayOutputStream();
        
        while (inputLen - offset > 0) {
            if (inputLen - offset > maxBlockSize) {
                cache = cipher.doFinal(data, offset, maxBlockSize);
            } else {
                cache = cipher.doFinal(data, offset, inputLen - offset);
            }
            out.write(cache, 0, cache.length);
            i++;
            offset = i * maxBlockSize;
        }
        
        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    /**
     * RSA分块解密
     */
    private byte[] decryptByBlocks(Cipher cipher, byte[] data, int maxBlockSize) throws Exception {
        int inputLen = data.length;
        int offset = 0;
        byte[] cache;
        int i = 0;
        
        java.io.ByteArrayOutputStream out = new java.io.ByteArrayOutputStream();
        
        while (inputLen - offset > 0) {
            if (inputLen - offset > maxBlockSize) {
                cache = cipher.doFinal(data, offset, maxBlockSize);
            } else {
                cache = cipher.doFinal(data, offset, inputLen - offset);
            }
            out.write(cache, 0, cache.length);
            i++;
            offset = i * maxBlockSize;
        }
        
        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    /**
     * 加载公钥
     */
    private PublicKey loadPublicKey(String publicKeyStr) throws Exception {
        String publicKeyContent = publicKeyStr
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");
        
        byte[] keyBytes = Base64.decode(publicKeyContent);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 加载私钥（支持PKCS#1和PKCS#8格式）
     */
    private PrivateKey loadPrivateKey(String privateKeyStr) throws Exception {
        // 清理私钥字符串，移除头尾标识和空白字符
        String privateKeyContent = privateKeyStr
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                .replace("-----END RSA PRIVATE KEY-----", "")
                .replaceAll("\\s", "");

        byte[] keyBytes = Base64.decode(privateKeyContent);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);

        // 首先尝试PKCS#8格式
        try {
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            return keyFactory.generatePrivate(pkcs8KeySpec);
        } catch (Exception e) {
            log.debug("PKCS#8格式解析失败，尝试PKCS#1格式: {}", e.getMessage());
        }

        // 如果PKCS#8失败，尝试PKCS#1格式（需要转换为PKCS#8）
        try {
            return loadPKCS1PrivateKey(keyBytes);
        } catch (Exception e) {
            log.error("私钥格式不支持，请确保使用PKCS#1或PKCS#8格式的RSA私钥", e);
            throw new Exception("私钥格式错误，支持的格式：PKCS#1 (-----BEGIN RSA PRIVATE KEY-----) 或 PKCS#8 (-----BEGIN PRIVATE KEY-----)", e);
        }
    }

    /**
     * 加载PKCS#1格式的RSA私钥并转换为PKCS#8格式
     */
    private PrivateKey loadPKCS1PrivateKey(byte[] pkcs1Bytes) throws Exception {
        // PKCS#1转PKCS#8的ASN.1结构
        // PKCS#8 = SEQUENCE {
        //   version INTEGER,
        //   privateKeyAlgorithm AlgorithmIdentifier,
        //   privateKey OCTET STRING (包含PKCS#1数据)
        // }

        // RSA算法标识符的ASN.1编码
        byte[] rsaAlgorithmIdentifier = {
            0x30, 0x0D, // SEQUENCE, length 13
            0x06, 0x09, 0x2A, (byte)0x86, 0x48, (byte)0x86, (byte)0xF7, 0x0D, 0x01, 0x01, 0x01, // RSA OID
            0x05, 0x00  // NULL
        };

        // 构建PKCS#8结构
        int pkcs8Length = 4 + rsaAlgorithmIdentifier.length + 2 + pkcs1Bytes.length;
        byte[] pkcs8Bytes = new byte[pkcs8Length + 4];

        int offset = 0;
        // SEQUENCE tag and length
        pkcs8Bytes[offset++] = 0x30;
        pkcs8Bytes[offset++] = (byte)0x82;
        pkcs8Bytes[offset++] = (byte)((pkcs8Length >> 8) & 0xFF);
        pkcs8Bytes[offset++] = (byte)(pkcs8Length & 0xFF);

        // version (INTEGER 0)
        pkcs8Bytes[offset++] = 0x02;
        pkcs8Bytes[offset++] = 0x01;
        pkcs8Bytes[offset++] = 0x00;

        // algorithm identifier
        System.arraycopy(rsaAlgorithmIdentifier, 0, pkcs8Bytes, offset, rsaAlgorithmIdentifier.length);
        offset += rsaAlgorithmIdentifier.length;

        // private key (OCTET STRING)
        pkcs8Bytes[offset++] = 0x04;
        pkcs8Bytes[offset++] = (byte)0x82;
        pkcs8Bytes[offset++] = (byte)((pkcs1Bytes.length >> 8) & 0xFF);
        pkcs8Bytes[offset++] = (byte)(pkcs1Bytes.length & 0xFF);
        System.arraycopy(pkcs1Bytes, 0, pkcs8Bytes, offset, pkcs1Bytes.length);

        // 使用转换后的PKCS#8数据创建私钥
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(pkcs8Bytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

}
