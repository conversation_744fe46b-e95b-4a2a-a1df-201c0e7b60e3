package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;

/**
 * 更新持卡人请求DTO
 */
@Data
public class UpdateCardholderRequestDTO {

    /**
     * 持卡人ID
     */
    private String cardholderId;

    /**
     * 姓
     */
    private String lastName;

    /**
     * 名
     */
    private String firstName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 区号
     */
    private String areaCode;

    /**
     * 地址
     */
    private String address;

    /**
     * 城市
     */
    private String city;

    /**
     * 州/省
     */
    private String state;

    /**
     * 国家
     */
    private String country;

    /**
     * 邮编
     */
    private String postCode;

}
