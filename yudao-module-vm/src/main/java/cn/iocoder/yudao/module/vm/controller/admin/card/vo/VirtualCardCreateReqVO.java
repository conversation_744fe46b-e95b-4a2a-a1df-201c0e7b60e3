package cn.iocoder.yudao.module.vm.controller.admin.card.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 虚拟卡创建 Request VO")
@Data
public class VirtualCardCreateReqVO {

    @Schema(description = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "Q493875YK")
    @NotBlank(message = "产品编码不能为空")
    private String productCode;

    @Schema(description = "持卡人名", requiredMode = Schema.RequiredMode.REQUIRED, example = "三")
    @NotBlank(message = "持卡人名不能为空")
    private String firstName;

    @Schema(description = "持卡人姓", requiredMode = Schema.RequiredMode.REQUIRED, example = "张")
    @NotBlank(message = "持卡人姓不能为空")
    private String lastName;

    @Schema(description = "卡内充值金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0.01", message = "充值金额必须大于0")
    private BigDecimal amount;

    @Schema(description = "卡标签", example = "测试卡")
    private String label;

    @Schema(description = "区号", example = "+86")
    private String areaCode;

    @Schema(description = "手机号", example = "13800138000")
    private String mobile;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "地址信息", example = "{\"address_line_one\":\"详细地址1\",\"city\":\"北京\",\"state\":\"北京\",\"country\":\"CN\",\"post_code\":\"100000\"}")
    private String cardAddress;

    @Schema(description = "厂商类型", example = "1")
    private Integer providerType;

    @Schema(description = "备注")
    private String remark;

}
