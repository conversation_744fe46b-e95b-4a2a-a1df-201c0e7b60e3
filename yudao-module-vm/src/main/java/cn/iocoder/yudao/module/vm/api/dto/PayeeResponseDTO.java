package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 收款方响应DTO
 */
@Data
public class PayeeResponseDTO {

    /**
     * 第三方收款方ID
     */
    private String thirdPartyPayeeId;

    /**
     * 收款方名称
     */
    private String payeeName;

    /**
     * 收款方类型
     */
    private String payeeType;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 收款方地址
     */
    private String address;

    /**
     * 城市
     */
    private String city;

    /**
     * 国家
     */
    private String country;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 第三方创建时间
     */
    private LocalDateTime thirdPartyCreateTime;

    /**
     * 第三方更新时间
     */
    private LocalDateTime thirdPartyUpdateTime;

}
