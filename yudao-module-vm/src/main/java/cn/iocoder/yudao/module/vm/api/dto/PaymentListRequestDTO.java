package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 付款列表请求DTO
 */
@Data
public class PaymentListRequestDTO {

    /**
     * 收款方ID
     */
    private String payeeId;

    /**
     * 付款状态
     */
    private Integer status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;

}
