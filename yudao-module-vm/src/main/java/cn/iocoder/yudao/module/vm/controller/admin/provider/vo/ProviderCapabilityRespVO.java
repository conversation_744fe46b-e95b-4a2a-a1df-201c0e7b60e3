package cn.iocoder.yudao.module.vm.controller.admin.provider.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 厂商能力响应 VO")
@Data
public class ProviderCapabilityRespVO {

    @Schema(description = "厂商类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer providerType;

    @Schema(description = "厂商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "VMCardIO")
    private String providerName;

    @Schema(description = "厂商编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "vmcardio")
    private String providerCode;

    @Schema(description = "是否可用", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean available = true;

    @Schema(description = "错误信息", example = "厂商配置未启用")
    private String errorMessage;

    @Schema(description = "是否支持账户管理能力", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean hasAccountCapability;

    @Schema(description = "是否支持卡片管理能力", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean hasCardManagementCapability;

    @Schema(description = "是否支持交易能力", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean hasTransactionCapability;

    @Schema(description = "是否支持持卡人管理能力", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private Boolean hasCardholderCapability;

    @Schema(description = "是否支持付款能力", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private Boolean hasPaymentCapability;

    @Schema(description = "支持的功能列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> supportedFeatures;

}
