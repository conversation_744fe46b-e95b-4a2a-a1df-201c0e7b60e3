package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 账户余额响应DTO
 */
@Data
public class AccountBalanceResponseDTO {

    /**
     * 账户总余额
     */
    private BigDecimal balance;

    /**
     * 钱包余额
     */
    private BigDecimal walletBalance;

    /**
     * 可用余额
     */
    private BigDecimal availableBalance;

    /**
     * 冻结余额
     */
    private BigDecimal frozenBalance;

}
