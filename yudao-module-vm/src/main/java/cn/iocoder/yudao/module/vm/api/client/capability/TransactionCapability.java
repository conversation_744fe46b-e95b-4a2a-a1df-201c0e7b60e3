package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 交易能力接口
 * 
 * 支持充值、退款、交易记录等功能
 */
public interface TransactionCapability {

    /**
     * 卡片充值
     * 
     * @param cardId 卡片ID
     * @param amount 充值金额
     * @return 充值结果
     */
    default ApiResponseDTO<Void> rechargeCard(String cardId, BigDecimal amount) {
        throw new UnsupportedOperationException("该厂商不支持卡片充值功能");
    }

    /**
     * 卡片退款
     * 
     * @param cardId 卡片ID
     * @param amount 退款金额
     * @return 退款结果
     */
    default ApiResponseDTO<Void> refundCard(String cardId, BigDecimal amount) {
        throw new UnsupportedOperationException("该厂商不支持卡片退款功能");
    }

    /**
     * 获取交易记录
     * 
     * @param request 交易记录请求
     * @return 交易记录响应
     */
    default ApiResponseDTO<List<TransactionResponseDTO>> getTransactions(TransactionRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持交易记录功能");
    }

    /**
     * 获取卡交易列表
     * 
     * @param request 卡交易请求
     * @return 卡交易列表响应
     */
    default ApiResponseDTO<List<TransactionResponseDTO>> getCardTransactions(CardTransactionRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持卡交易列表功能");
    }

    /**
     * 获取交易详情
     * 
     * @param transactionId 交易ID
     * @return 交易详情响应
     */
    default ApiResponseDTO<TransactionResponseDTO> getTransactionDetail(String transactionId) {
        throw new UnsupportedOperationException("该厂商不支持交易详情功能");
    }

}
