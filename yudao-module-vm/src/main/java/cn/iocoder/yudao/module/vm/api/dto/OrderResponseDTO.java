package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单响应DTO
 */
@Data
public class OrderResponseDTO {

    /**
     * 第三方订单ID
     */
    private String thirdPartyOrderId;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 订单描述
     */
    private String description;

    /**
     * 第三方创建时间
     */
    private LocalDateTime thirdPartyCreateTime;

    /**
     * 第三方更新时间
     */
    private LocalDateTime thirdPartyUpdateTime;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 扩展信息
     */
    private String extInfo;

}
