package cn.iocoder.yudao.module.vm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 厂商类型枚举
 */
@AllArgsConstructor
@Getter
public enum ProviderTypeEnum {

    VMCARDIO(1, "vmcardio", "VMCardIO", "https://sandbox-api.vmcardio.com/"),
    LEMFT(2, "lemft", "LEMFT", "https://api.lemft.com/");

    /**
     * 类型值
     */
    private final Integer type;

    /**
     * 厂商编码
     */
    private final String code;

    /**
     * 厂商名称
     */
    private final String name;

    /**
     * API基础URL
     */
    private final String baseUrl;

    /**
     * 根据类型获取枚举
     */
    public static ProviderTypeEnum getByType(Integer type) {
        for (ProviderTypeEnum providerEnum : values()) {
            if (providerEnum.getType().equals(type)) {
                return providerEnum;
            }
        }
        return null;
    }

    /**
     * 根据编码获取枚举
     */
    public static ProviderTypeEnum getByCode(String code) {
        for (ProviderTypeEnum providerEnum : values()) {
            if (providerEnum.getCode().equals(code)) {
                return providerEnum;
            }
        }
        return null;
    }
}
