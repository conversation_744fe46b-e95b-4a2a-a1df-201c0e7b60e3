package cn.iocoder.yudao.module.vm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交易类型枚举
 */
@AllArgsConstructor
@Getter
public enum TransactionTypeEnum {

    CREATE_CARD(1, "创建卡片"),
    RECHARGE(2, "充值"),
    REFUND(3, "退款"),
    FREEZE(4, "冻结"),
    UNFREEZE(5, "解冻"),
    DELETE_CARD(6, "删除卡片"),
    CONSUMPTION(7, "消费"),
    REVERSAL(8, "冲正");

    /**
     * 类型值
     */
    private final Integer type;
    
    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据类型获取枚举
     */
    public static TransactionTypeEnum getByType(Integer type) {
        for (TransactionTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
