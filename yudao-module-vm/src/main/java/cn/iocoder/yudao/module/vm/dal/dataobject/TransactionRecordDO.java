package cn.iocoder.yudao.module.vm.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易记录 DO
 *
 * <AUTHOR>
 */
@TableName("vm_transaction_record")
@KeySequence("vm_transaction_record_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionRecordDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * 虚拟卡ID
     */
    private Long cardId;
    
    /**
     * 第三方卡片ID
     */
    private String thirdPartyCardId;
    
    /**
     * 第三方交易ID
     */
    private String thirdPartyTransactionId;
    
    /**
     * 交易类型：1-创建卡片，2-充值，3-退款，4-冻结，5-解冻，6-删除卡片，7-消费，8-冲正
     */
    private Integer transactionType;
    
    /**
     * 交易金额
     */
    private BigDecimal amount;
    
    /**
     * 交易前余额
     */
    private BigDecimal balanceBefore;
    
    /**
     * 交易后余额
     */
    private BigDecimal balanceAfter;
    
    /**
     * 交易状态：1-成功，2-失败，3-处理中
     */
    private Integer status;
    
    /**
     * 厂商类型
     */
    private Integer providerType;
    
    /**
     * 交易描述
     */
    private String description;
    
    /**
     * 第三方交易时间
     */
    private LocalDateTime thirdPartyTransactionTime;
    
    /**
     * 失败原因
     */
    private String failureReason;
    
    /**
     * 扩展信息（JSON格式）
     */
    private String extInfo;
    
    /**
     * 备注
     */
    private String remark;

}
