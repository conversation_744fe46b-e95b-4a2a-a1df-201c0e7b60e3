package cn.iocoder.yudao.module.vm.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.vm.controller.admin.transaction.vo.TransactionRecordPageReqVO;
import cn.iocoder.yudao.module.vm.dal.dataobject.TransactionRecordDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 交易记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransactionRecordMapper extends BaseMapperX<TransactionRecordDO> {

    default PageResult<TransactionRecordDO> selectPage(TransactionRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TransactionRecordDO>()
                .eqIfPresent(TransactionRecordDO::getCardId, reqVO.getCardId())
                .likeIfPresent(TransactionRecordDO::getThirdPartyCardId, reqVO.getThirdPartyCardId())
                .eqIfPresent(TransactionRecordDO::getTransactionType, reqVO.getTransactionType())
                .eqIfPresent(TransactionRecordDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TransactionRecordDO::getProviderType, reqVO.getProviderType())
                .betweenIfPresent(TransactionRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TransactionRecordDO::getId));
    }

    default List<TransactionRecordDO> selectListByCardId(Long cardId) {
        return selectList(TransactionRecordDO::getCardId, cardId);
    }

    default List<TransactionRecordDO> selectListByThirdPartyCardId(String thirdPartyCardId) {
        return selectList(TransactionRecordDO::getThirdPartyCardId, thirdPartyCardId);
    }

    default TransactionRecordDO selectByThirdPartyTransactionId(String thirdPartyTransactionId) {
        return selectOne(TransactionRecordDO::getThirdPartyTransactionId, thirdPartyTransactionId);
    }

}
