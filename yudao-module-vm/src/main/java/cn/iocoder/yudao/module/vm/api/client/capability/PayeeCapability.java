package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.util.List;

/**
 * 收款方管理能力接口
 * 
 * 支持运营资金来源、预期付款目的、收款方管理等功能
 */
public interface PayeeCapability {

    /**
     * 获取运营资金来源列表
     * 
     * @return 运营资金来源列表响应
     */
    default ApiResponseDTO<List<FundSourceResponseDTO>> getFundSourceList() {
        throw new UnsupportedOperationException("该厂商不支持运营资金来源功能");
    }

    /**
     * 获取预期付款目的列表
     * 
     * @return 预期付款目的列表响应
     */
    default ApiResponseDTO<List<PaymentPurposeResponseDTO>> getPaymentPurposeList() {
        throw new UnsupportedOperationException("该厂商不支持预期付款目的功能");
    }

    /**
     * 创建收款方
     * 
     * @param request 创建收款方请求
     * @return 创建收款方响应
     */
    default ApiResponseDTO<PayeeResponseDTO> createPayee(CreatePayeeRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持创建收款方功能");
    }

    /**
     * 编辑收款方
     * 
     * @param request 编辑收款方请求
     * @return 编辑结果
     */
    default ApiResponseDTO<Void> updatePayee(UpdatePayeeRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持编辑收款方功能");
    }

    /**
     * 申请收款方
     * 
     * @param request 申请收款方请求
     * @return 申请结果
     */
    default ApiResponseDTO<Void> applyPayee(ApplyPayeeRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持申请收款方功能");
    }

    /**
     * 删除收款方
     * 
     * @param payeeId 收款方ID
     * @return 删除结果
     */
    default ApiResponseDTO<Void> deletePayee(String payeeId) {
        throw new UnsupportedOperationException("该厂商不支持删除收款方功能");
    }

    /**
     * 获取收款方列表
     * 
     * @param request 收款方列表请求
     * @return 收款方列表响应
     */
    default ApiResponseDTO<List<PayeeResponseDTO>> getPayeeList(PayeeListRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持收款方列表功能");
    }

    /**
     * 获取收款方详情
     * 
     * @param payeeId 收款方ID
     * @return 收款方详情响应
     */
    default ApiResponseDTO<PayeeResponseDTO> getPayeeDetail(String payeeId) {
        throw new UnsupportedOperationException("该厂商不支持收款方详情功能");
    }

}
