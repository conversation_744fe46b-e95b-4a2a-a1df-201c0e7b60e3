package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.util.List;

/**
 * 付款能力接口
 * 
 * 支持收款方管理、付款等功能
 */
public interface PaymentCapability {

    /**
     * 获取兑换汇率
     * 
     * @param request 汇率请求
     * @return 汇率响应
     */
    default ApiResponseDTO<ExchangeRateResponseDTO> getExchangeRate(ExchangeRateRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持汇率查询功能");
    }

    /**
     * 创建收款方
     * 
     * @param request 创建收款方请求
     * @return 创建收款方响应
     */
    default ApiResponseDTO<PayeeResponseDTO> createPayee(CreatePayeeRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持创建收款方功能");
    }

    /**
     * 编辑收款方
     * 
     * @param request 编辑收款方请求
     * @return 编辑结果
     */
    default ApiResponseDTO<Void> updatePayee(UpdatePayeeRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持编辑收款方功能");
    }

    /**
     * 获取收款方列表
     * 
     * @param request 收款方列表请求
     * @return 收款方列表响应
     */
    default ApiResponseDTO<List<PayeeResponseDTO>> getPayeeList(PayeeListRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持收款方列表功能");
    }

    /**
     * 获取收款方详情
     * 
     * @param payeeId 收款方ID
     * @return 收款方详情响应
     */
    default ApiResponseDTO<PayeeResponseDTO> getPayeeDetail(String payeeId) {
        throw new UnsupportedOperationException("该厂商不支持收款方详情功能");
    }

    /**
     * 删除收款方
     * 
     * @param payeeId 收款方ID
     * @return 删除结果
     */
    default ApiResponseDTO<Void> deletePayee(String payeeId) {
        throw new UnsupportedOperationException("该厂商不支持删除收款方功能");
    }

    /**
     * 创建付款
     * 
     * @param request 创建付款请求
     * @return 创建付款响应
     */
    default ApiResponseDTO<PaymentResponseDTO> createPayment(CreatePaymentRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持创建付款功能");
    }

    /**
     * 获取付款列表
     * 
     * @param request 付款列表请求
     * @return 付款列表响应
     */
    default ApiResponseDTO<List<PaymentResponseDTO>> getPaymentList(PaymentListRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持付款列表功能");
    }

    /**
     * 获取付款详情
     * 
     * @param paymentId 付款ID
     * @return 付款详情响应
     */
    default ApiResponseDTO<PaymentResponseDTO> getPaymentDetail(String paymentId) {
        throw new UnsupportedOperationException("该厂商不支持付款详情功能");
    }

}
