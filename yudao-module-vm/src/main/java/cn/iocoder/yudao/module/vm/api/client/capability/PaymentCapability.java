package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.util.List;

/**
 * 付款能力接口
 *
 * 支持付款原因、创建付款、付款列表、付款详情等功能
 */
public interface PaymentCapability {

    /**
     * 获取付款原因列表
     *
     * @return 付款原因列表响应
     */
    default ApiResponseDTO<List<PaymentReasonResponseDTO>> getPaymentReasons() {
        throw new UnsupportedOperationException("该厂商不支持付款原因功能");
    }

    /**
     * 创建付款
     *
     * @param request 创建付款请求
     * @return 创建付款响应
     */
    default ApiResponseDTO<PaymentResponseDTO> createPayment(CreatePaymentRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持创建付款功能");
    }

    /**
     * 获取付款列表
     *
     * @param request 付款列表请求
     * @return 付款列表响应
     */
    default ApiResponseDTO<List<PaymentResponseDTO>> getPaymentList(PaymentListRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持付款列表功能");
    }

    /**
     * 获取付款详情
     *
     * @param paymentId 付款ID
     * @return 付款详情响应
     */
    default ApiResponseDTO<PaymentResponseDTO> getPaymentDetail(String paymentId) {
        throw new UnsupportedOperationException("该厂商不支持付款详情功能");
    }

}
