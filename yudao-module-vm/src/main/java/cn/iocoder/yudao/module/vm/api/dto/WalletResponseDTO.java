package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 钱包响应DTO
 */
@Data
public class WalletResponseDTO {

    /**
     * 钱包ID
     */
    private String walletId;

    /**
     * 钱包名称
     */
    private String walletName;

    /**
     * 钱包类型
     */
    private String walletType;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 可用余额
     */
    private BigDecimal availableBalance;

    /**
     * 冻结余额
     */
    private BigDecimal frozenBalance;

    /**
     * 币种
     */
    private String currency;

    /**
     * 状态
     */
    private Integer status;

}
