package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.util.List;

/**
 * 钱包管理能力接口
 *
 * 支持钱包列表、详情、交易流水等功能
 */
public interface WalletCapability {

    /**
     * 获取账户余额
     *
     * @return 账户余额响应
     */
    default ApiResponseDTO<AccountBalanceResponseDTO> getAccountBalance() {
        throw new UnsupportedOperationException("该厂商不支持账户余额功能");
    }

    /**
     * 获取钱包列表
     *
     * @return 钱包列表响应
     */
    default ApiResponseDTO<List<WalletResponseDTO>> getWalletList() {
        throw new UnsupportedOperationException("该厂商不支持钱包列表功能");
    }

    /**
     * 获取钱包详情
     *
     * @param walletId 钱包ID
     * @return 钱包详情响应
     */
    default ApiResponseDTO<WalletResponseDTO> getWalletDetail(String walletId) {
        throw new UnsupportedOperationException("该厂商不支持钱包详情功能");
    }

    /**
     * 获取交易流水列表
     *
     * @param request 交易流水请求
     * @return 交易流水列表响应
     */
    default ApiResponseDTO<List<TransactionResponseDTO>> getTransactionList(TransactionRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持交易流水列表功能");
    }

    /**
     * 获取交易详情
     *
     * @param transactionId 交易ID
     * @return 交易详情响应
     */
    default ApiResponseDTO<TransactionResponseDTO> getTransactionDetail(String transactionId) {
        throw new UnsupportedOperationException("该厂商不支持交易详情功能");
    }

    /**
     * 获取兑换汇率
     *
     * @param request 汇率请求
     * @return 汇率响应
     */
    default ApiResponseDTO<ExchangeRateResponseDTO> getExchangeRate(ExchangeRateRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持汇率查询功能");
    }

}
