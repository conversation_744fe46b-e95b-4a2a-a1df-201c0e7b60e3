package cn.iocoder.yudao.module.vm.api.client;

import cn.iocoder.yudao.module.vm.api.client.base.BaseVirtualCardApiClient;
import cn.iocoder.yudao.module.vm.api.client.capability.*;

/**
 * 虚拟卡API客户端接口
 *
 * 定义了与第三方虚拟卡服务交互的标准接口
 * 支持多厂商实现，通过策略模式进行切换
 *
 * 采用能力组合模式，不同厂商可以实现不同的能力接口
 */
public interface VirtualCardApiClient extends
        BaseVirtualCardApiClient,
        WalletCapability,
        CardBinCapability,
        CardManagementCapability,
        OrderCapability,
        CardholderCapability,
        PayMethodCapability,
        PayeeCapability,
        PaymentCapability {

}
