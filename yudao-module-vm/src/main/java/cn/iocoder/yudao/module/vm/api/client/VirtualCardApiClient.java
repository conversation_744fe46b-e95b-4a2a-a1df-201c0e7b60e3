package cn.iocoder.yudao.module.vm.api.client;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 虚拟卡API客户端接口
 *
 * 定义了与第三方虚拟卡服务交互的标准接口
 * 支持多厂商实现，通过策略模式进行切换
 */
public interface VirtualCardApiClient {

    /**
     * 获取账户余额
     *
     * @return 账户余额响应
     */
    ApiResponseDTO<AccountBalanceResponseDTO> getAccountBalance();

    /**
     * 获取卡产品列表
     *
     * @return 卡产品列表响应
     */
    ApiResponseDTO<List<CardProductResponseDTO>> getCardProducts();

    /**
     * 创建虚拟卡
     *
     * @param request 创建卡片请求
     * @return 创建卡片响应
     */
    ApiResponseDTO<CreateCardResponseDTO> createCard(CreateCardRequestDTO request);

    /**
     * 获取卡片详情
     *
     * @param request 卡片详情请求
     * @return 卡片详情响应
     */
    ApiResponseDTO<CardDetailResponseDTO> getCardDetail(CardDetailRequestDTO request);

    /**
     * 修改卡片限额
     *
     * @param request 修改限额请求
     * @return 修改结果
     */
    ApiResponseDTO<Void> updateCardLimit(UpdateCardLimitRequestDTO request);

    /**
     * 冻结卡片
     *
     * @param cardId 卡片ID
     * @return 冻结结果
     */
    ApiResponseDTO<Void> freezeCard(String cardId);

    /**
     * 解冻卡片
     *
     * @param cardId 卡片ID
     * @return 解冻结果
     */
    ApiResponseDTO<Void> unfreezeCard(String cardId);

    /**
     * 卡片充值
     *
     * @param cardId 卡片ID
     * @param amount 充值金额
     * @return 充值结果
     */
    ApiResponseDTO<Void> rechargeCard(String cardId, BigDecimal amount);

    /**
     * 卡片退款
     *
     * @param cardId 卡片ID
     * @param amount 退款金额
     * @return 退款结果
     */
    ApiResponseDTO<Void> refundCard(String cardId, BigDecimal amount);

    /**
     * 获取交易记录
     *
     * @param request 交易记录请求
     * @return 交易记录响应
     */
    ApiResponseDTO<List<TransactionResponseDTO>> getTransactions(TransactionRequestDTO request);

    /**
     * 删除卡片
     *
     * @param cardId 卡片ID
     * @return 删除结果
     */
    ApiResponseDTO<Void> deleteCard(String cardId);

    /**
     * 获取厂商类型
     *
     * @return 厂商类型
     */
    Integer getProviderType();

    /**
     * 获取访问令牌
     *
     * @return 访问令牌信息
     */
    TokenResult getAccessToken();

    /**
     * 检查令牌是否有效
     *
     * @return 是否有效
     */
    boolean isTokenValid();

}
