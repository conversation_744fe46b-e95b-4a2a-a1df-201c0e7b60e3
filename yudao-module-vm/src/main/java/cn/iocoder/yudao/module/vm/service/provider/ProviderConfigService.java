package cn.iocoder.yudao.module.vm.service.provider;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.vm.controller.admin.provider.vo.ProviderConfigPageReqVO;
import cn.iocoder.yudao.module.vm.controller.admin.provider.vo.ProviderConfigSaveReqVO;

import cn.iocoder.yudao.module.vm.dal.dataobject.ProviderConfigDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 厂商配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ProviderConfigService {

    /**
     * 创建厂商配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProviderConfig(@Valid ProviderConfigSaveReqVO createReqVO);

    /**
     * 更新厂商配置
     *
     * @param updateReqVO 更新信息
     */
    void updateProviderConfig(@Valid ProviderConfigSaveReqVO updateReqVO);

    /**
     * 删除厂商配置
     *
     * @param id 编号
     */
    void deleteProviderConfig(Long id);

    /**
     * 获得厂商配置
     *
     * @param id 编号
     * @return 厂商配置
     */
    ProviderConfigDO getProviderConfig(Long id);

    /**
     * 获得厂商配置分页
     *
     * @param pageReqVO 分页查询
     * @return 厂商配置分页
     */
    PageResult<ProviderConfigDO> getProviderConfigPage(ProviderConfigPageReqVO pageReqVO);

    /**
     * 根据厂商类型获取配置
     *
     * @param providerType 厂商类型
     * @return 厂商配置
     */
    ProviderConfigDO getByProviderType(Integer providerType);

    /**
     * 获取默认厂商配置
     *
     * @return 默认厂商配置
     */
    ProviderConfigDO getDefaultProvider();

    /**
     * 获取启用的厂商配置列表
     *
     * @return 启用的厂商配置列表
     */
    List<ProviderConfigDO> getEnabledProviderList();

    /**
     * 设置默认厂商
     *
     * @param id 厂商配置ID
     */
    void setDefaultProvider(Long id);

    /**
     * 启用/禁用厂商
     *
     * @param id 厂商配置ID
     * @param enabled 是否启用
     */
    void updateProviderStatus(Long id, Boolean enabled);

    /**
     * 更新厂商token信息
     *
     * @param id 厂商配置ID
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param tokenExpireTime 令牌过期时间
     */
    void updateProviderToken(Long id, String accessToken, String refreshToken,
                           java.time.LocalDateTime tokenExpireTime);

}
