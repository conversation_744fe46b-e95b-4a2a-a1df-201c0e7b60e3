package cn.iocoder.yudao.module.vm.controller.admin.provider;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.vm.api.client.VirtualCardApiClientFactory;
import cn.iocoder.yudao.module.vm.api.client.capability.*;
import cn.iocoder.yudao.module.vm.controller.admin.provider.vo.ProviderCapabilityRespVO;
import cn.iocoder.yudao.module.vm.enums.ProviderTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 厂商能力管理控制器
 */
@Tag(name = "管理后台 - 厂商能力管理")
@RestController
@RequestMapping("/vm/provider-capability")
@RequiredArgsConstructor
public class ProviderCapabilityController {

    private final VirtualCardApiClientFactory apiClientFactory;

    @GetMapping("/list")
    @Operation(summary = "获取厂商能力列表")
    @PreAuthorize("@ss.hasPermission('vm:provider-config:query')")
    public CommonResult<List<ProviderCapabilityRespVO>> getProviderCapabilities() {
        List<ProviderCapabilityRespVO> result = new ArrayList<>();
        
        // 获取所有支持的厂商类型
        Set<Integer> supportedProviders = apiClientFactory.getSupportedProviderTypes();
        
        for (Integer providerType : supportedProviders) {
            try {
                var apiClient = apiClientFactory.getApiClient(providerType);
                ProviderTypeEnum providerEnum = ProviderTypeEnum.getByType(providerType);
                
                ProviderCapabilityRespVO capability = new ProviderCapabilityRespVO();
                capability.setProviderType(providerType);
                capability.setProviderName(providerEnum != null ? providerEnum.getName() : "未知厂商");
                capability.setProviderCode(providerEnum != null ? providerEnum.getCode() : "unknown");
                
                // 检查各种能力
                capability.setHasAccountCapability(apiClient instanceof AccountCapability);
                capability.setHasCardManagementCapability(apiClient instanceof CardManagementCapability);
                capability.setHasTransactionCapability(apiClient instanceof TransactionCapability);
                capability.setHasCardholderCapability(apiClient instanceof CardholderCapability);
                capability.setHasPaymentCapability(apiClient instanceof PaymentCapability);
                
                // 检查具体功能支持
                checkSpecificCapabilities(apiClient, capability);
                
                result.add(capability);
                
            } catch (Exception e) {
                // 如果厂商不可用，仍然显示基本信息
                ProviderTypeEnum providerEnum = ProviderTypeEnum.getByType(providerType);
                ProviderCapabilityRespVO capability = new ProviderCapabilityRespVO();
                capability.setProviderType(providerType);
                capability.setProviderName(providerEnum != null ? providerEnum.getName() : "未知厂商");
                capability.setProviderCode(providerEnum != null ? providerEnum.getCode() : "unknown");
                capability.setAvailable(false);
                capability.setErrorMessage(e.getMessage());
                result.add(capability);
            }
        }
        
        return success(result);
    }

    /**
     * 检查具体功能支持情况
     */
    private void checkSpecificCapabilities(Object apiClient, ProviderCapabilityRespVO capability) {
        List<String> supportedFeatures = new ArrayList<>();
        
        // 基础功能（所有厂商都支持）
        supportedFeatures.add("创建虚拟卡");
        supportedFeatures.add("获取卡片详情");
        supportedFeatures.add("冻结卡片");
        supportedFeatures.add("解冻卡片");
        supportedFeatures.add("删除卡片");
        
        // 账户管理功能
        if (apiClient instanceof AccountCapability) {
            supportedFeatures.add("获取账户余额");
            
            // 检查钱包功能
            try {
                ((AccountCapability) apiClient).getWalletList();
                supportedFeatures.add("钱包管理");
            } catch (UnsupportedOperationException e) {
                // 不支持钱包功能
            }
        }
        
        // 卡片管理功能
        if (apiClient instanceof CardManagementCapability) {
            CardManagementCapability cardMgmt = (CardManagementCapability) apiClient;
            
            try {
                cardMgmt.getCardProducts();
                supportedFeatures.add("获取卡产品");
            } catch (UnsupportedOperationException e) {}
            
            try {
                cardMgmt.getCardBinList();
                supportedFeatures.add("卡Bin管理");
            } catch (UnsupportedOperationException e) {}
            
            try {
                cardMgmt.activateCard("test");
                supportedFeatures.add("卡激活");
            } catch (UnsupportedOperationException e) {}
            
            try {
                cardMgmt.updateCardLimit(null);
                supportedFeatures.add("修改卡限额");
            } catch (UnsupportedOperationException | NullPointerException e) {
                if (!(e instanceof NullPointerException)) {
                    // NullPointerException说明支持但参数为空
                    supportedFeatures.add("修改卡限额");
                }
            }
        }
        
        // 交易功能
        if (apiClient instanceof TransactionCapability) {
            TransactionCapability transaction = (TransactionCapability) apiClient;
            
            try {
                transaction.rechargeCard("test", null);
                supportedFeatures.add("卡片充值");
            } catch (UnsupportedOperationException e) {}
            
            try {
                transaction.refundCard("test", null);
                supportedFeatures.add("卡片退款");
            } catch (UnsupportedOperationException e) {}
            
            try {
                transaction.getTransactions(null);
                supportedFeatures.add("交易记录查询");
            } catch (UnsupportedOperationException e) {}
        }
        
        // 持卡人管理功能
        if (apiClient instanceof CardholderCapability) {
            supportedFeatures.add("持卡人管理");
        }
        
        // 付款功能
        if (apiClient instanceof PaymentCapability) {
            PaymentCapability payment = (PaymentCapability) apiClient;
            
            try {
                payment.getExchangeRate(null);
                supportedFeatures.add("汇率查询");
            } catch (UnsupportedOperationException e) {}
            
            try {
                payment.createPayee(null);
                supportedFeatures.add("收款方管理");
            } catch (UnsupportedOperationException e) {}
            
            try {
                payment.createPayment(null);
                supportedFeatures.add("付款功能");
            } catch (UnsupportedOperationException e) {}
        }
        
        capability.setSupportedFeatures(supportedFeatures);
    }

}
