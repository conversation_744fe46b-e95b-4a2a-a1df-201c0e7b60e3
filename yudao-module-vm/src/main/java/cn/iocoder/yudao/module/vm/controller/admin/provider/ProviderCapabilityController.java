package cn.iocoder.yudao.module.vm.controller.admin.provider;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.vm.api.client.VirtualCardApiClient;
import cn.iocoder.yudao.module.vm.api.client.VirtualCardApiClientFactory;
import cn.iocoder.yudao.module.vm.api.client.capability.*;
import cn.iocoder.yudao.module.vm.controller.admin.provider.vo.ProviderCapabilityRespVO;
import cn.iocoder.yudao.module.vm.enums.ProviderTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 厂商能力管理控制器
 */
@Tag(name = "管理后台 - 厂商能力管理")
@RestController
@RequestMapping("/vm/provider-capability")
@RequiredArgsConstructor
public class ProviderCapabilityController {

    private final VirtualCardApiClientFactory apiClientFactory;

    @GetMapping("/list")
    @Operation(summary = "获取厂商能力列表")
    @PreAuthorize("@ss.hasPermission('vm:provider-config:query')")
    public CommonResult<List<ProviderCapabilityRespVO>> getProviderCapabilities() {
        List<ProviderCapabilityRespVO> result = new ArrayList<>();
        
        // 获取所有支持的厂商类型
        Set<Integer> supportedProviders = apiClientFactory.getSupportedProviderTypes();
        
        for (Integer providerType : supportedProviders) {
            try {
                VirtualCardApiClient apiClient = apiClientFactory.getApiClient(providerType);
                ProviderTypeEnum providerEnum = ProviderTypeEnum.getByType(providerType);
                
                ProviderCapabilityRespVO capability = new ProviderCapabilityRespVO();
                capability.setProviderType(providerType);
                capability.setProviderName(providerEnum != null ? providerEnum.getName() : "未知厂商");
                capability.setProviderCode(providerEnum != null ? providerEnum.getCode() : "unknown");
                
                // 检查各种能力
                capability.setHasWalletCapability(apiClient instanceof WalletCapability);
                capability.setHasCardBinCapability(apiClient instanceof CardBinCapability);
                capability.setHasCardManagementCapability(apiClient instanceof CardManagementCapability);
                capability.setHasOrderCapability(apiClient instanceof OrderCapability);
                capability.setHasCardholderCapability(apiClient instanceof CardholderCapability);
                capability.setHasPayMethodCapability(apiClient instanceof PayMethodCapability);
                capability.setHasPayeeCapability(apiClient instanceof PayeeCapability);
                capability.setHasPaymentCapability(apiClient instanceof PaymentCapability);
                
                // 检查具体功能支持
                checkSpecificCapabilities(apiClient, capability);
                
                result.add(capability);
                
            } catch (Exception e) {
                // 如果厂商不可用，仍然显示基本信息
                ProviderTypeEnum providerEnum = ProviderTypeEnum.getByType(providerType);
                ProviderCapabilityRespVO capability = new ProviderCapabilityRespVO();
                capability.setProviderType(providerType);
                capability.setProviderName(providerEnum != null ? providerEnum.getName() : "未知厂商");
                capability.setProviderCode(providerEnum != null ? providerEnum.getCode() : "unknown");
                capability.setAvailable(false);
                capability.setErrorMessage(e.getMessage());
                result.add(capability);
            }
        }
        
        return success(result);
    }

    /**
     * 检查具体功能支持情况
     */
    private void checkSpecificCapabilities(Object apiClient, ProviderCapabilityRespVO capability) {
        List<String> supportedFeatures = new ArrayList<>();
        
        // 基础功能（所有厂商都支持）
        supportedFeatures.add("创建虚拟卡");
        supportedFeatures.add("获取卡片详情");
        supportedFeatures.add("冻结卡片");
        supportedFeatures.add("解冻卡片");
        supportedFeatures.add("删除卡片");
        
        // 钱包管理功能
        if (apiClient instanceof WalletCapability) {
            WalletCapability wallet = (WalletCapability) apiClient;

            try {
                wallet.getAccountBalance();
                supportedFeatures.add("获取账户余额");
            } catch (UnsupportedOperationException e) {}

            try {
                wallet.getWalletList();
                supportedFeatures.add("钱包列表");
            } catch (UnsupportedOperationException e) {}

            try {
                wallet.getWalletDetail("test");
                supportedFeatures.add("钱包详情");
            } catch (UnsupportedOperationException e) {}

            try {
                wallet.getTransactionList(null);
                supportedFeatures.add("交易流水列表");
            } catch (UnsupportedOperationException e) {}

            try {
                wallet.getTransactionDetail("test");
                supportedFeatures.add("交易详情");
            } catch (UnsupportedOperationException e) {}

            try {
                wallet.getExchangeRate(null);
                supportedFeatures.add("汇率查询");
            } catch (UnsupportedOperationException e) {}
        }

        // 卡Bin管理功能
        if (apiClient instanceof CardBinCapability) {
            CardBinCapability cardBin = (CardBinCapability) apiClient;

            try {
                cardBin.getCardBinList();
                supportedFeatures.add("卡Bin列表");
            } catch (UnsupportedOperationException e) {}

            try {
                cardBin.getCardBinDetail("test");
                supportedFeatures.add("卡Bin详情");
            } catch (UnsupportedOperationException e) {}

            try {
                cardBin.getCardBinFee("test");
                supportedFeatures.add("卡Bin费用");
            } catch (UnsupportedOperationException e) {}

            try {
                cardBin.getCardBinLimit("test");
                supportedFeatures.add("卡Bin限制");
            } catch (UnsupportedOperationException e) {}
        }
        
        // 卡片管理功能
        if (apiClient instanceof CardManagementCapability) {
            CardManagementCapability cardMgmt = (CardManagementCapability) apiClient;

            try {
                cardMgmt.getCardProducts();
                supportedFeatures.add("获取卡产品");
            } catch (UnsupportedOperationException e) {}

            try {
                cardMgmt.getCardList(null);
                supportedFeatures.add("卡列表");
            } catch (UnsupportedOperationException e) {}

            try {
                cardMgmt.activateCard("test");
                supportedFeatures.add("卡激活");
            } catch (UnsupportedOperationException e) {}

            try {
                cardMgmt.updateCardLimit(null);
                supportedFeatures.add("修改卡限额");
            } catch (UnsupportedOperationException | NullPointerException e) {
                if (!(e instanceof NullPointerException)) {
                    supportedFeatures.add("修改卡限额");
                }
            }

            try {
                cardMgmt.getCardTransactions(null);
                supportedFeatures.add("卡交易列表");
            } catch (UnsupportedOperationException e) {}

            try {
                cardMgmt.getMerchantTransactionScore(null);
                supportedFeatures.add("商户交易评分");
            } catch (UnsupportedOperationException e) {}
        }

        // 订单管理功能
        if (apiClient instanceof OrderCapability) {
            OrderCapability order = (OrderCapability) apiClient;

            try {
                order.openCard(null);
                supportedFeatures.add("开卡");
            } catch (UnsupportedOperationException e) {}

            try {
                order.rechargeCard("test", null);
                supportedFeatures.add("卡充值");
            } catch (UnsupportedOperationException e) {}

            try {
                order.getOrderList(null);
                supportedFeatures.add("订单列表");
            } catch (UnsupportedOperationException e) {}

            try {
                order.getOrderDetail("test");
                supportedFeatures.add("订单详情");
            } catch (UnsupportedOperationException e) {}
        }
        
        // 持卡人管理功能
        if (apiClient instanceof CardholderCapability) {
            CardholderCapability cardholder = (CardholderCapability) apiClient;

            try {
                cardholder.getCardholderList(null);
                supportedFeatures.add("持卡人列表");
            } catch (UnsupportedOperationException e) {}

            try {
                cardholder.createCardholder(null);
                supportedFeatures.add("创建持卡人");
            } catch (UnsupportedOperationException e) {}

            try {
                cardholder.updateCardholder(null);
                supportedFeatures.add("编辑持卡人");
            } catch (UnsupportedOperationException e) {}

            try {
                cardholder.getCardholderDetail("test");
                supportedFeatures.add("持卡人详情");
            } catch (UnsupportedOperationException e) {}

            try {
                cardholder.deleteCardholder("test");
                supportedFeatures.add("删除持卡人");
            } catch (UnsupportedOperationException e) {}
        }

        // 付款方式功能
        if (apiClient instanceof PayMethodCapability) {
            PayMethodCapability payMethod = (PayMethodCapability) apiClient;

            try {
                payMethod.getPayMethodList();
                supportedFeatures.add("付款方式");
            } catch (UnsupportedOperationException e) {}
        }

        // 收款方管理功能
        if (apiClient instanceof PayeeCapability) {
            PayeeCapability payee = (PayeeCapability) apiClient;

            try {
                payee.getFundSourceList();
                supportedFeatures.add("运营资金来源列表");
            } catch (UnsupportedOperationException e) {}

            try {
                payee.getPaymentPurposeList();
                supportedFeatures.add("预期付款目的列表");
            } catch (UnsupportedOperationException e) {}

            try {
                payee.createPayee(null);
                supportedFeatures.add("创建收款方");
            } catch (UnsupportedOperationException e) {}

            try {
                payee.updatePayee(null);
                supportedFeatures.add("编辑收款方");
            } catch (UnsupportedOperationException e) {}

            try {
                payee.applyPayee(null);
                supportedFeatures.add("申请收款方");
            } catch (UnsupportedOperationException e) {}

            try {
                payee.deletePayee("test");
                supportedFeatures.add("删除收款方");
            } catch (UnsupportedOperationException e) {}

            try {
                payee.getPayeeList(null);
                supportedFeatures.add("收款方列表");
            } catch (UnsupportedOperationException e) {}

            try {
                payee.getPayeeDetail("test");
                supportedFeatures.add("收款方详情");
            } catch (UnsupportedOperationException e) {}
        }

        // 付款功能
        if (apiClient instanceof PaymentCapability) {
            PaymentCapability payment = (PaymentCapability) apiClient;

            try {
                payment.getPaymentReasons();
                supportedFeatures.add("付款原因");
            } catch (UnsupportedOperationException e) {}

            try {
                payment.createPayment(null);
                supportedFeatures.add("创建付款");
            } catch (UnsupportedOperationException e) {}

            try {
                payment.getPaymentList(null);
                supportedFeatures.add("付款列表");
            } catch (UnsupportedOperationException e) {}

            try {
                payment.getPaymentDetail("test");
                supportedFeatures.add("付款详情");
            } catch (UnsupportedOperationException e) {}
        }
        
        capability.setSupportedFeatures(supportedFeatures);
    }

}
