package cn.iocoder.yudao.module.vm.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * 加密解密工具类
 * 
 * 实现RSA加密解密功能，用于与第三方API的数据交互
 */
@Slf4j
public class EncryptionUtil {

    private static final String RSA_ALGORITHM = "RSA";
    private static final String RSA_TRANSFORMATION = "RSA/ECB/PKCS1Padding";
    private static final String RSA_OAEP_TRANSFORMATION = "RSA/ECB/OAEPPadding";
    private static final String AES_ALGORITHM = "AES";
    private static final String AES_GCM_TRANSFORMATION = "AES/GCM/NoPadding";

    /**
     * 加密结果类
     */
    @Data
    public static class EncryptionResult {
        private String encryptedContent;  // 加密后的bizContent
        private String encryptedKey;      // 加密后的AES密钥
        private String signature;         // 签名
    }

    /**
     * 使用公钥加密数据
     * 
     * @param data 需要加密的数据
     * @param publicKeyStr 公钥字符串
     * @return 加密后的十六进制字符串
     */
    public static String encryptDataWithPublicKey(Object data, String publicKeyStr) {
        try {
            // 将数据转换为JSON字符串
            String jsonData = JSONUtil.toJsonStr(data);
            
            // 加载公钥
            PublicKey publicKey = loadPublicKey(publicKeyStr);
            
            // 创建加密器
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            
            // 加密数据
            byte[] encryptedData = cipher.doFinal(jsonData.getBytes(StandardCharsets.UTF_8));
            
            // Base64编码
            String base64EncodedData = Base64.encode(encryptedData);
            
            // 十六进制编码
            return HexUtil.encodeHexStr(base64EncodedData.getBytes(StandardCharsets.UTF_8));
            
        } catch (Exception e) {
            log.error("数据加密失败", e);
            throw new RuntimeException("数据加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按照文档格式加密bizContent
     *
     * @param jsonStr 业务内容
     * @param publicKeyStr 公钥字符串
     * @return 加密结果
     */
    public static String encryptJson(String jsonStr, String publicKeyStr) {
        try {
            log.info("输入JSON: {}", jsonStr);

            // 1. 使用RSA公钥分块加密JSON数据
            PublicKey publicKey = loadPublicKey(publicKeyStr);
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);

            // RSA加密的最大数据长度（2048位密钥使用PKCS1Padding时为245字节）
            int maxEncryptBlock = 245;
            byte[] data = jsonStr.getBytes(StandardCharsets.UTF_8);

            // 分块加密
            byte[] encryptedData = encryptByBlocks(cipher, data, maxEncryptBlock);

            // 2. Base64编码
            String base64EncodedData = Base64.encode(encryptedData);

            // 3. 十六进制编码
            String hexEncodedData = HexUtil.encodeHexStr(base64EncodedData.getBytes(StandardCharsets.UTF_8));

            // 4. 构建返回结果
            Map<String, String> result = new HashMap<>();
            result.put("content", hexEncodedData);

            String resultJson = JSONUtil.toJsonPrettyStr(result);
            log.info("加密结果: {}", resultJson);

            return resultJson;

        } catch (Exception e) {
            log.error("JSON加密失败", e);
            Map<String, String> errorResult = new HashMap<>();
            errorResult.put("error", "加密失败: " + e.getMessage());
            return JSONUtil.toJsonPrettyStr(errorResult);
        }
    }

    /**
     * RSA分块加密
     */
    private static byte[] encryptByBlocks(Cipher cipher, byte[] data, int maxBlockSize) throws Exception {
        int inputLen = data.length;
        int offset = 0;
        byte[] cache;
        int i = 0;

        // 创建输出流来存储加密结果
        java.io.ByteArrayOutputStream out = new java.io.ByteArrayOutputStream();

        // 对数据分段加密
        while (inputLen - offset > 0) {
            if (inputLen - offset > maxBlockSize) {
                cache = cipher.doFinal(data, offset, maxBlockSize);
            } else {
                cache = cipher.doFinal(data, offset, inputLen - offset);
            }
            out.write(cache, 0, cache.length);
            i++;
            offset = i * maxBlockSize;
        }

        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    /**
     * 使用私钥解密数据
     * 
     * @param encryptedData 加密的十六进制字符串数据
     * @param privateKeyStr 私钥字符串
     * @return 解密后的JSON字符串
     */
    /**
     * 使用RSA私钥解密数据
     *
     * @param encryptedContent 加密的十六进制字符串
     * @param privateKeyStr 私钥字符串
     * @return 解密后的JSON字符串
     */
    public static String decryptJson(String encryptedContent, String privateKeyStr) {
        try {
            log.info("解密content: {}", encryptedContent);

            // 1. 十六进制解码
            byte[] hexDecodedData = HexUtil.decodeHex(encryptedContent);
            String base64EncodedData = new String(hexDecodedData, StandardCharsets.UTF_8);

            // 2. Base64解码
            byte[] base64DecodedData = Base64.decode(base64EncodedData);

            // 3. 使用RSA私钥解密
            PrivateKey privateKey = loadPrivateKey(privateKeyStr);
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);

            // 分块解密数据（RSA解密块大小为256字节，对应2048位密钥）
            byte[] decryptedData = decryptByBlocks(cipher, base64DecodedData, 256);

            // 4. 转换为JSON字符串
            String decryptedJson = new String(decryptedData, StandardCharsets.UTF_8);

            log.info("解密结果: {}", decryptedJson);
            return decryptedJson;

        } catch (Exception e) {
            log.error("解密失败", e);
            return "解密失败: " + e.getMessage();
        }
    }

    /**
     * RSA分块解密
     */
    private static byte[] decryptByBlocks(Cipher cipher, byte[] data, int maxBlockSize) throws Exception {
        int inputLen = data.length;
        int offset = 0;
        byte[] cache;
        int i = 0;

        // 创建输出流来存储解密结果
        java.io.ByteArrayOutputStream out = new java.io.ByteArrayOutputStream();

        // 对数据分段解密
        while (inputLen - offset > 0) {
            if (inputLen - offset > maxBlockSize) {
                cache = cipher.doFinal(data, offset, maxBlockSize);
            } else {
                cache = cipher.doFinal(data, offset, inputLen - offset);
            }
            out.write(cache, 0, cache.length);
            i++;
            offset = i * maxBlockSize;
        }

        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    /**
     * 加载公钥
     * 
     * @param publicKeyStr 公钥字符串
     * @return 公钥对象
     */
    private static PublicKey loadPublicKey(String publicKeyStr) throws Exception {
        // 移除公钥头尾标识
        String publicKeyContent = publicKeyStr
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");
        
        // Base64解码
        byte[] keyBytes = Base64.decode(publicKeyContent);
        
        // 生成公钥
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 加载私钥
     * 
     * @param privateKeyStr 私钥字符串
     * @return 私钥对象
     */
    private static PrivateKey loadPrivateKey(String privateKeyStr) throws Exception {
        // 移除私钥头尾标识
        String privateKeyContent = privateKeyStr
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                .replace("-----END RSA PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
        
        // Base64解码
        byte[] keyBytes = Base64.decode(privateKeyContent);
        
        // 生成私钥
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

}
