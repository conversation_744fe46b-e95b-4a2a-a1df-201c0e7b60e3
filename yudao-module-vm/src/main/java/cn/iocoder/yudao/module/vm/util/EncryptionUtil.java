package cn.iocoder.yudao.module.vm.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * 加密解密工具类
 * 
 * 实现RSA加密解密功能，用于与第三方API的数据交互
 */
@Slf4j
public class EncryptionUtil {

    private static final String RSA_ALGORITHM = "RSA";
    private static final String RSA_TRANSFORMATION = "RSA/ECB/PKCS1Padding";
    private static final String RSA_OAEP_TRANSFORMATION = "RSA/ECB/OAEPPadding";
    private static final String AES_ALGORITHM = "AES";
    private static final String AES_GCM_TRANSFORMATION = "AES/GCM/NoPadding";

    /**
     * 加密结果类
     */
    @Data
    public static class EncryptionResult {
        private String encryptedContent;  // 加密后的bizContent
        private String encryptedKey;      // 加密后的AES密钥
        private String signature;         // 签名
    }

    /**
     * 使用公钥加密数据
     * 
     * @param data 需要加密的数据
     * @param publicKeyStr 公钥字符串
     * @return 加密后的十六进制字符串
     */
    public static String encryptDataWithPublicKey(Object data, String publicKeyStr) {
        try {
            // 将数据转换为JSON字符串
            String jsonData = JSONUtil.toJsonStr(data);
            
            // 加载公钥
            PublicKey publicKey = loadPublicKey(publicKeyStr);
            
            // 创建加密器
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            
            // 加密数据
            byte[] encryptedData = cipher.doFinal(jsonData.getBytes(StandardCharsets.UTF_8));
            
            // Base64编码
            String base64EncodedData = Base64.encode(encryptedData);
            
            // 十六进制编码
            return HexUtil.encodeHexStr(base64EncodedData.getBytes(StandardCharsets.UTF_8));
            
        } catch (Exception e) {
            log.error("数据加密失败", e);
            throw new RuntimeException("数据加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按照文档格式加密bizContent
     *
     * @param bizContent 业务内容
     * @param publicKeyStr 公钥字符串
     * @return 加密结果
     */
    public static EncryptionResult encryptBizContent(Object bizContent, String publicKeyStr) {
        try {
            // 1. 将bizContent转换为JSON字符串
            String jsonData = JSONUtil.toJsonStr(bizContent);

            // 2. 生成AES密钥
            KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
            keyGenerator.init(256);
            SecretKey aesKey = keyGenerator.generateKey();

            // 3. 使用AES-GCM加密bizContent
            Cipher aesCipher = Cipher.getInstance(AES_GCM_TRANSFORMATION);
            aesCipher.init(Cipher.ENCRYPT_MODE, aesKey);
            byte[] encryptedData = aesCipher.doFinal(jsonData.getBytes(StandardCharsets.UTF_8));

            // 获取IV（初始化向量）
            byte[] iv = aesCipher.getIV();

            // 组合IV和加密数据
            byte[] encryptedWithIv = new byte[iv.length + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, iv.length);
            System.arraycopy(encryptedData, 0, encryptedWithIv, iv.length, encryptedData.length);

            // Base64编码加密后的内容
            String encryptedContent = Base64.encode(encryptedWithIv);

            // 4. 使用RSA-OAEP加密AES密钥
            PublicKey publicKey = loadPublicKey(publicKeyStr);
            Cipher rsaCipher = Cipher.getInstance(RSA_OAEP_TRANSFORMATION);
            rsaCipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encryptedAesKey = rsaCipher.doFinal(aesKey.getEncoded());
            String encryptedKey = Base64.encode(encryptedAesKey);

            // 5. 生成签名（这里简化处理，实际应该使用私钥签名）
            String signature = generateSignature(jsonData, aesKey.getEncoded());

            // 6. 返回结果
            EncryptionResult result = new EncryptionResult();
            result.setEncryptedContent(encryptedContent);
            result.setEncryptedKey(encryptedKey);
            result.setSignature(signature);

            return result;

        } catch (Exception e) {
            log.error("bizContent加密失败", e);
            throw new RuntimeException("bizContent加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成签名（简化实现）
     */
    private static String generateSignature(String data, byte[] key) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(data.getBytes(StandardCharsets.UTF_8));
            digest.update(key);
            byte[] hash = digest.digest();
            return Base64.encode(hash);
        } catch (Exception e) {
            log.error("生成签名失败", e);
            return "";
        }
    }

    /**
     * 使用私钥解密数据
     * 
     * @param encryptedData 加密的十六进制字符串数据
     * @param privateKeyStr 私钥字符串
     * @return 解密后的JSON字符串
     */
    public static String decryptDataWithPrivateKey(String encryptedData, String privateKeyStr) {
        try {
            // 十六进制解码
            byte[] hexDecodedData = HexUtil.decodeHex(encryptedData);
            
            // Base64解码
            byte[] base64DecodedData = Base64.decode(new String(hexDecodedData, StandardCharsets.UTF_8));
            
            // 加载私钥
            PrivateKey privateKey = loadPrivateKey(privateKeyStr);
            
            // 创建解密器
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            
            // 解密数据
            byte[] decryptedData = cipher.doFinal(base64DecodedData);
            
            // 返回解密后的字符串
            return new String(decryptedData, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            log.error("数据解密失败", e);
            throw new RuntimeException("数据解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 加载公钥
     * 
     * @param publicKeyStr 公钥字符串
     * @return 公钥对象
     */
    private static PublicKey loadPublicKey(String publicKeyStr) throws Exception {
        // 移除公钥头尾标识
        String publicKeyContent = publicKeyStr
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");
        
        // Base64解码
        byte[] keyBytes = Base64.decode(publicKeyContent);
        
        // 生成公钥
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 加载私钥
     * 
     * @param privateKeyStr 私钥字符串
     * @return 私钥对象
     */
    private static PrivateKey loadPrivateKey(String privateKeyStr) throws Exception {
        // 移除私钥头尾标识
        String privateKeyContent = privateKeyStr
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replace("-----BEGIN RSA PRIVATE KEY-----", "")
                .replace("-----END RSA PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
        
        // Base64解码
        byte[] keyBytes = Base64.decode(privateKeyContent);
        
        // 生成私钥
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

}
