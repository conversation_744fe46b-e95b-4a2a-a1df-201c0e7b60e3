package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 卡产品响应DTO
 */
@Data
public class CardProductResponseDTO {

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 卡类型（储值卡/额度卡）
     */
    private String cardType;

    /**
     * 最小金额
     */
    private BigDecimal minAmount;

    /**
     * 最大金额
     */
    private BigDecimal maxAmount;

    /**
     * 单笔最大限额
     */
    private BigDecimal maxSingleLimit;

    /**
     * 日限额
     */
    private BigDecimal maxDayLimit;

    /**
     * 月限额
     */
    private BigDecimal maxMonthLimit;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 产品描述
     */
    private String description;

}
