package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单管理能力接口
 * 
 * 支持开卡、卡充值、订单列表、订单详情等功能
 */
public interface OrderCapability {

    /**
     * 开卡
     * 
     * @param request 开卡请求
     * @return 开卡响应
     */
    default ApiResponseDTO<CreateCardResponseDTO> openCard(OpenCardRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持开卡功能");
    }

    /**
     * 卡充值
     * 
     * @param cardId 卡片ID
     * @param amount 充值金额
     * @return 充值结果
     */
    default ApiResponseDTO<Void> rechargeCard(String cardId, BigDecimal amount) {
        throw new UnsupportedOperationException("该厂商不支持卡片充值功能");
    }

    /**
     * 获取订单列表
     * 
     * @param request 订单列表请求
     * @return 订单列表响应
     */
    default ApiResponseDTO<List<OrderResponseDTO>> getOrderList(OrderListRequestDTO request) {
        throw new UnsupportedOperationException("该厂商不支持订单列表功能");
    }

    /**
     * 获取订单详情
     * 
     * @param orderId 订单ID
     * @return 订单详情响应
     */
    default ApiResponseDTO<OrderResponseDTO> getOrderDetail(String orderId) {
        throw new UnsupportedOperationException("该厂商不支持订单详情功能");
    }

}
