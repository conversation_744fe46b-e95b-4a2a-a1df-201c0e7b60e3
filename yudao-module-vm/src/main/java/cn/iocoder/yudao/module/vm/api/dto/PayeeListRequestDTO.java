package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 收款方列表请求DTO
 */
@Data
public class PayeeListRequestDTO {

    /**
     * 收款方名称
     */
    private String payeeName;

    /**
     * 收款方类型
     */
    private String payeeType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;

}
