package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.util.List;

/**
 * 卡Bin管理能力接口
 * 
 * 支持卡Bin列表、详情、费用、限制等功能
 */
public interface CardBinCapability {

    /**
     * 获取卡Bin列表
     * 
     * @return 卡Bin列表响应
     */
    default ApiResponseDTO<List<CardBinResponseDTO>> getCardBinList() {
        throw new UnsupportedOperationException("该厂商不支持卡Bin列表功能");
    }

    /**
     * 获取卡Bin详情
     * 
     * @param binId Bin ID
     * @return 卡Bin详情响应
     */
    default ApiResponseDTO<CardBinResponseDTO> getCardBinDetail(String binId) {
        throw new UnsupportedOperationException("该厂商不支持卡Bin详情功能");
    }

    /**
     * 获取卡Bin费用
     * 
     * @param binId Bin ID
     * @return 卡Bin费用响应
     */
    default ApiResponseDTO<CardBinFeeResponseDTO> getCardBinFee(String binId) {
        throw new UnsupportedOperationException("该厂商不支持卡Bin费用功能");
    }

    /**
     * 获取卡Bin限制
     * 
     * @param binId Bin ID
     * @return 卡Bin限制响应
     */
    default ApiResponseDTO<CardBinLimitResponseDTO> getCardBinLimit(String binId) {
        throw new UnsupportedOperationException("该厂商不支持卡Bin限制功能");
    }

}
