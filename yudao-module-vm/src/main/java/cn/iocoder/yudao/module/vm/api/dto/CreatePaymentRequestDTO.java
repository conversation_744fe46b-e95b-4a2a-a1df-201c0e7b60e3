package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 创建付款请求DTO
 */
@Data
public class CreatePaymentRequestDTO {

    /**
     * 收款方ID
     */
    private String payeeId;

    /**
     * 付款金额
     */
    private BigDecimal amount;

    /**
     * 付款币种
     */
    private String currency;

    /**
     * 付款原因ID
     */
    private String reasonId;

    /**
     * 付款描述
     */
    private String description;

    /**
     * 备注
     */
    private String remark;

}
