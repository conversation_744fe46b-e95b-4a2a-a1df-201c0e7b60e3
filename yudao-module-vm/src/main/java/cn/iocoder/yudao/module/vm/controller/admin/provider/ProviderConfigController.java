package cn.iocoder.yudao.module.vm.controller.admin.provider;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
// import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
// import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.vm.controller.admin.provider.vo.*;
import cn.iocoder.yudao.module.vm.dal.dataobject.ProviderConfigDO;
import cn.iocoder.yudao.module.vm.enums.ProviderTypeEnum;
import cn.iocoder.yudao.module.vm.service.provider.ProviderConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 厂商配置")
@RestController
@RequestMapping("/vm/provider-config")
@Validated
public class ProviderConfigController {

    @Resource
    private ProviderConfigService providerConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建厂商配置")
    @PreAuthorize("@ss.hasPermission('vm:provider-config:create')")
    public CommonResult<Long> createProviderConfig(@Valid @RequestBody ProviderConfigSaveReqVO createReqVO) {
        return success(providerConfigService.createProviderConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新厂商配置")
    @PreAuthorize("@ss.hasPermission('vm:provider-config:update')")
    public CommonResult<Boolean> updateProviderConfig(@Valid @RequestBody ProviderConfigSaveReqVO updateReqVO) {
        providerConfigService.updateProviderConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除厂商配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('vm:provider-config:delete')")
    public CommonResult<Boolean> deleteProviderConfig(@RequestParam("id") Long id) {
        providerConfigService.deleteProviderConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得厂商配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('vm:provider-config:query')")
    public CommonResult<ProviderConfigRespVO> getProviderConfig(@RequestParam("id") Long id) {
        ProviderConfigDO providerConfig = providerConfigService.getProviderConfig(id);
        return success(BeanUtils.toBean(providerConfig, ProviderConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得厂商配置分页")
    @PreAuthorize("@ss.hasPermission('vm:provider-config:query')")
    public CommonResult<PageResult<ProviderConfigRespVO>> getProviderConfigPage(@Valid ProviderConfigPageReqVO pageReqVO) {
        PageResult<ProviderConfigDO> pageResult = providerConfigService.getProviderConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProviderConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出厂商配置 Excel")
    @PreAuthorize("@ss.hasPermission('vm:provider-config:export')")
    public void exportProviderConfigExcel(@Valid ProviderConfigPageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProviderConfigDO> list = providerConfigService.getProviderConfigPage(pageReqVO).getList();
        // 导出 Excel - 需要引入相关依赖
         List<ProviderConfigRespVO> respList = BeanUtils.toBean(list, ProviderConfigRespVO.class);
         ExcelUtils.write(response, "厂商配置.xls", "数据", ProviderConfigRespVO.class, respList);

        // 临时实现：返回JSON格式数据
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("Excel导出功能需要引入相关依赖");
    }

    @GetMapping("/list-enabled")
    @Operation(summary = "获取启用的厂商配置列表")
    @PreAuthorize("@ss.hasPermission('vm:provider-config:query')")
    public CommonResult<List<ProviderConfigRespVO>> getEnabledProviderList() {
        List<ProviderConfigDO> list = providerConfigService.getEnabledProviderList();
        return success(BeanUtils.toBean(list, ProviderConfigRespVO.class));
    }

    @PostMapping("/set-default")
    @Operation(summary = "设置默认厂商")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('vm:provider-config:update')")
    public CommonResult<Boolean> setDefaultProvider(@RequestParam("id") Long id) {
        providerConfigService.setDefaultProvider(id);
        return success(true);
    }

    @PostMapping("/update-status")
    @Operation(summary = "更新厂商状态")
    @PreAuthorize("@ss.hasPermission('vm:provider-config:update')")
    public CommonResult<Boolean> updateProviderStatus(@RequestParam("id") Long id,
                                                      @RequestParam("enabled") Boolean enabled) {
        providerConfigService.updateProviderStatus(id, enabled);
        return success(true);
    }


}
