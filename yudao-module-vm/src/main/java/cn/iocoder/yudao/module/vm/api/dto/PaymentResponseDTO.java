package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 付款响应DTO
 */
@Data
public class PaymentResponseDTO {

    /**
     * 第三方付款ID
     */
    private String thirdPartyPaymentId;

    /**
     * 收款方ID
     */
    private String payeeId;

    /**
     * 收款方名称
     */
    private String payeeName;

    /**
     * 付款金额
     */
    private BigDecimal amount;

    /**
     * 付款币种
     */
    private String currency;

    /**
     * 付款原因
     */
    private String reason;

    /**
     * 付款描述
     */
    private String description;

    /**
     * 付款状态
     */
    private Integer status;

    /**
     * 第三方创建时间
     */
    private LocalDateTime thirdPartyCreateTime;

    /**
     * 第三方更新时间
     */
    private LocalDateTime thirdPartyUpdateTime;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 扩展信息
     */
    private String extInfo;

}
