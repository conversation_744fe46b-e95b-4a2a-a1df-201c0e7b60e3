package cn.iocoder.yudao.module.vm.api.client.capability;

import cn.iocoder.yudao.module.vm.api.dto.*;
import java.util.List;

/**
 * 付款方式管理能力接口
 * 
 * 支持付款方式查询等功能
 */
public interface PayMethodCapability {

    /**
     * 获取付款方式列表
     * 
     * @return 付款方式列表响应
     */
    default ApiResponseDTO<List<PayMethodResponseDTO>> getPayMethodList() {
        throw new UnsupportedOperationException("该厂商不支持付款方式功能");
    }

}
