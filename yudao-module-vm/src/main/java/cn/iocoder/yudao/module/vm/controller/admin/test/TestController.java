package cn.iocoder.yudao.module.vm.controller.admin.test;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 虚拟卡测试")
@RestController
@RequestMapping("/vm/test")
public class TestController {

    @GetMapping("/test1")
    @Operation(summary = "测试接口")
    public CommonResult<String> test1() {
        return success("虚拟卡模块运行正常");
    }
}
