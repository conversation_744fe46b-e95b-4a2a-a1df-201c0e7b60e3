package cn.iocoder.yudao.module.vm.api.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 修改卡片限额请求DTO
 */
@Data
public class UpdateCardLimitRequestDTO {

    /**
     * 卡片ID
     */
    private String cardId;

    /**
     * 单笔限额
     */
    private BigDecimal singleLimit;

    /**
     * 日限额
     */
    private BigDecimal dayLimit;

    /**
     * 月限额
     */
    private BigDecimal monthLimit;

}
