-- 虚拟卡模块数据库表结构

-- 虚拟卡表
CREATE TABLE `vm_virtual_card` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `third_party_card_id` varchar(100) NOT NULL COMMENT '第三方卡片ID',
  `card_number` varchar(50) DEFAULT NULL COMMENT '卡号',
  `cvv` varchar(10) DEFAULT NULL COMMENT 'CVV',
  `expire_date` varchar(10) DEFAULT NULL COMMENT '有效期',
  `holder_name` varchar(100) NOT NULL COMMENT '持卡人姓名',
  `first_name` varchar(50) NOT NULL COMMENT '持卡人名',
  `last_name` varchar(50) NOT NULL COMMENT '持卡人姓',
  `available_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `card_type` varchar(20) DEFAULT NULL COMMENT '卡类型',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '卡状态：1-激活，2-禁用，3-删除，4-冻结',
  `provider_type` tinyint NOT NULL COMMENT '厂商类型：1-vmcardio，2-厂商B，3-厂商C',
  `product_code` varchar(50) NOT NULL COMMENT '产品编码',
  `label` varchar(100) DEFAULT NULL COMMENT '卡标签',
  `area_code` varchar(10) DEFAULT NULL COMMENT '区号',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `card_address` text COMMENT '地址信息（JSON格式）',
  `third_party_create_time` datetime DEFAULT NULL COMMENT '第三方创建时间',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_third_party_card_id` (`third_party_card_id`),
  KEY `idx_status` (`status`),
  KEY `idx_provider_type` (`provider_type`),
  KEY `idx_holder_name` (`holder_name`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='虚拟卡表';

-- 厂商配置表
CREATE TABLE `vm_provider_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `provider_type` tinyint NOT NULL COMMENT '厂商类型：1-vmcardio，2-厂商B，3-厂商C',
  `provider_name` varchar(100) NOT NULL COMMENT '厂商名称',
  `base_url` varchar(200) NOT NULL COMMENT 'API基础URL',
  `app_id` varchar(200) NOT NULL COMMENT 'App ID（应用ID）',
  `app_secret` varchar(200) NOT NULL COMMENT 'App 密钥（应用密钥）',
  `public_key` text COMMENT '厂商公钥（用于加密请求给厂商）',
  `private_key` text COMMENT '我自己的私钥（用于解密厂商响应）',
  `enabled` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否启用：0-禁用，1-启用',
  `is_default` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否为默认厂商：0-否，1-是',
  `timeout` int NOT NULL DEFAULT '30' COMMENT '超时时间（秒）',
  `retry_count` int NOT NULL DEFAULT '3' COMMENT '重试次数',
  `ip_whitelist` varchar(500) DEFAULT NULL COMMENT 'IP白名单（多个IP用逗号分隔）',
  `ext_config` text COMMENT '扩展配置（JSON格式）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_provider_type` (`provider_type`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='厂商配置表';

-- 交易记录表
CREATE TABLE `vm_transaction_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `card_id` bigint NOT NULL COMMENT '虚拟卡ID',
  `third_party_card_id` varchar(100) NOT NULL COMMENT '第三方卡片ID',
  `third_party_transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方交易ID',
  `transaction_type` tinyint NOT NULL COMMENT '交易类型：1-创建卡片，2-充值，3-退款，4-冻结，5-解冻，6-删除卡片，7-消费，8-冲正',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '交易金额',
  `balance_before` decimal(10,2) DEFAULT NULL COMMENT '交易前余额',
  `balance_after` decimal(10,2) DEFAULT NULL COMMENT '交易后余额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '交易状态：1-成功，2-失败，3-处理中',
  `provider_type` tinyint NOT NULL COMMENT '厂商类型',
  `description` varchar(200) DEFAULT NULL COMMENT '交易描述',
  `third_party_transaction_time` datetime DEFAULT NULL COMMENT '第三方交易时间',
  `failure_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `ext_info` text COMMENT '扩展信息（JSON格式）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_third_party_card_id` (`third_party_card_id`),
  KEY `idx_third_party_transaction_id` (`third_party_transaction_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_status` (`status`),
  KEY `idx_provider_type` (`provider_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易记录表';

-- 插入默认厂商配置（示例数据）
INSERT INTO `vm_provider_config` (`provider_type`, `provider_name`, `base_url`, `app_id`, `app_secret`, `enabled`, `is_default`, `timeout`, `retry_count`, `remark`)
VALUES (1, 'VMCardIO', 'https://sandbox-api.vmcardio.com/', 'your_app_id_here', 'your_app_secret_here', 1, 1, 30, 3, 'VMCardIO厂商配置');
