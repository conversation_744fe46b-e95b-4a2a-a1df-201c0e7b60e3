-- 厂商配置表字段更新迁移脚本
-- 将 api_key 字段改为 app_id，并添加 app_secret 字段

-- 1. 添加新字段 app_id 和 app_secret
ALTER TABLE `vm_provider_config` 
ADD COLUMN `app_id` varchar(200) NOT NULL COMMENT 'App ID（应用ID）' AFTER `base_url`,
ADD COLUMN `app_secret` varchar(200) NOT NULL COMMENT 'App 密钥（应用密钥）' AFTER `app_id`;

-- 2. 将原有 api_key 数据迁移到 app_id
UPDATE `vm_provider_config` SET `app_id` = `api_key` WHERE `api_key` IS NOT NULL;

-- 3. 删除原有的 api_key 字段
ALTER TABLE `vm_provider_config` DROP COLUMN `api_key`;

-- 4. 更新字段注释，使其更清晰
ALTER TABLE `vm_provider_config` 
MODIFY COLUMN `public_key` text COMMENT '厂商公钥（用于加密请求给厂商）',
MODIFY COLUMN `private_key` text COMMENT '我自己的私钥（用于解密厂商响应）';

-- 5. 更新现有数据的 app_secret（需要根据实际情况填入正确的值）
-- 注意：这里需要手动设置正确的 app_secret 值
UPDATE `vm_provider_config` SET `app_secret` = 'your_app_secret_here' WHERE `app_secret` = '';

-- 6. 验证数据迁移结果
SELECT id, provider_name, app_id, app_secret, public_key IS NOT NULL as has_public_key, private_key IS NOT NULL as has_private_key 
FROM `vm_provider_config`;
