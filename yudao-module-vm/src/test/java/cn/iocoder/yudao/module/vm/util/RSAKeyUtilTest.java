package cn.iocoder.yudao.module.vm.util;

import org.junit.jupiter.api.Test;

import java.security.PrivateKey;
import java.security.PublicKey;

/**
 * RSA密钥工具测试类
 */
public class RSAKeyUtilTest {

    @Test
    public void testDetectPrivateKeyFormat() {
        String pkcs1Key = "-----BEGIN RSA PRIVATE KEY-----\nMIIEpAIBAAKCAQEA...\n-----END RSA PRIVATE KEY-----";
        String pkcs8Key = "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----";
        
        System.out.println("PKCS1 格式检测: " + RSAKeyUtil.detectPrivateKeyFormat(pkcs1Key));
        System.out.println("PKCS8 格式检测: " + RSAKeyUtil.detectPrivateKeyFormat(pkcs8Key));
    }

    @Test
    public void testLoadPrivateKey() {
        // 这里需要替换为实际的私钥进行测试
        String testPrivateKey = """
            -----BEGIN PRIVATE KEY-----
            MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDGtJKOZK8Z9X7V
            your_actual_private_key_content_here
            -----END PRIVATE KEY-----
            """;
        
        try {
            PrivateKey privateKey = RSAKeyUtil.loadPrivateKey(testPrivateKey);
            System.out.println("私钥加载成功: " + privateKey.getAlgorithm());
            System.out.println("私钥格式: " + privateKey.getFormat());
        } catch (Exception e) {
            System.err.println("私钥加载失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testLoadPublicKey() {
        // 这里需要替换为实际的公钥进行测试
        String testPublicKey = """
            -----BEGIN PUBLIC KEY-----
            MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxrSSjmSvGfV+1Q
            your_actual_public_key_content_here
            -----END PUBLIC KEY-----
            """;
        
        try {
            PublicKey publicKey = RSAKeyUtil.loadPublicKey(testPublicKey);
            System.out.println("公钥加载成功: " + publicKey.getAlgorithm());
            System.out.println("公钥格式: " + publicKey.getFormat());
        } catch (Exception e) {
            System.err.println("公钥加载失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试加密解密流程
     */
    @Test
    public void testEncryptionDecryption() {
        // 注意：这个测试需要有效的密钥对才能运行
        String testData = "Hello, World!";
        
        try {
            // 这里需要替换为实际的密钥
            String publicKeyStr = "your_public_key_here";
            String privateKeyStr = "your_private_key_here";
            
            PublicKey publicKey = RSAKeyUtil.loadPublicKey(publicKeyStr);
            PrivateKey privateKey = RSAKeyUtil.loadPrivateKey(privateKeyStr);
            
            System.out.println("密钥加载成功，可以进行加密解密测试");
            
        } catch (Exception e) {
            System.err.println("密钥测试失败: " + e.getMessage());
        }
    }
}
