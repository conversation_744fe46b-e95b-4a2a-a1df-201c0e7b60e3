package cn.iocoder.yudao.module.vm.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * VMCardIO 加密工具类
 * 
 * 提供JSON加密功能，返回加密后的content
 */
@Slf4j
public class VmCardIoEncryptUtil {

    // 默认公钥（请替换为实际的公钥）
    private static final String DEFAULT_PUBLIC_KEY = "-----BEGIN PUBLIC KEY-----\n" +
            "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvu7kCs1KansAjpjwNgbC\n" +
            "/g0Qwc0laUq/CzCVy1qjin/MIcMyeoLBhwCq36wsEcJUXHwDlZM2IXj3/6pT204i\n" +
            "ckJhf15oj280fU1bbbXzV2VesVsbo4Y4MXDIwYzNtXp5rgoZC/Txc95Io8JJHGzL\n" +
            "pTT+CcGiezrcBTJJxUwBVGtUYu/evyP0dToqDAqw5KZUtjteU0ME50omrrO3ACyN\n" +
            "h13XtNRSLprz8UeslMrL3Pd1aCSPrrrVG9A4ut2vqaXuAEdES3UfjBV8bxeAeXVj\n" +
            "8ItPuTWR0n1Piu5MNKf+uOGNaCkTAR4cIwiqv34/1K3hppwBMXMaYliTEZNkRyxj\n" +
            "6QIDAQAB\n" +
            "-----END PUBLIC KEY-----\n";

    private static final String RSA_ALGORITHM = "RSA";
    private static final String RSA_OAEP_TRANSFORMATION = "RSA/ECB/OAEPPadding";
    private static final String AES_ALGORITHM = "AES";
    private static final String AES_GCM_TRANSFORMATION = "AES/GCM/NoPadding";

    /**
     * 加密JSON数据
     *
     * @param jsonStr 输入的JSON字符串
     * @return 加密结果，格式：{"content": "加密后的十六进制字符串"}
     */
    public static String encryptJson(String jsonStr) {
        return encryptJson(jsonStr, DEFAULT_PUBLIC_KEY);
    }

    /**
     * 简单加密JSON数据（只返回加密后的bizContent）
     *
     * @param jsonStr 输入的JSON字符串
     * @param publicKeyStr 公钥字符串
     * @return 加密结果，格式：{"content": "加密后的bizContent"}
     */
    public static String encryptJsonSimple(String jsonStr, String publicKeyStr) {
        try {
            log.info("输入JSON: {}", jsonStr);

            // 1. 生成AES密钥
            KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
            keyGenerator.init(256);
            SecretKey aesKey = keyGenerator.generateKey();

            // 2. 使用AES-GCM加密JSON数据
            Cipher aesCipher = Cipher.getInstance(AES_GCM_TRANSFORMATION);
            aesCipher.init(Cipher.ENCRYPT_MODE, aesKey);
            byte[] encryptedData = aesCipher.doFinal(jsonStr.getBytes(StandardCharsets.UTF_8));

            // 获取IV（初始化向量）
            byte[] iv = aesCipher.getIV();

            // 组合IV和加密数据
            byte[] encryptedWithIv = new byte[iv.length + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, iv.length);
            System.arraycopy(encryptedData, 0, encryptedWithIv, iv.length, encryptedData.length);

            // Base64编码加密后的内容
            String encryptedContent = Base64.encode(encryptedWithIv);

            // 3. 构建返回结果（只返回加密后的bizContent）
            Map<String, String> result = new HashMap<>();
            result.put("content", encryptedContent);

            String resultJson = JSONUtil.toJsonPrettyStr(result);
            log.info("加密结果: {}", resultJson);

            return resultJson;

        } catch (Exception e) {
            log.error("JSON加密失败", e);
            Map<String, String> errorResult = new HashMap<>();
            errorResult.put("error", "加密失败: " + e.getMessage());
            return JSONUtil.toJsonPrettyStr(errorResult);
        }
    }

    /**
     * 简单加密JSON数据（使用默认公钥）
     */
    public static String encryptJsonSimple(String jsonStr) {
        return encryptJsonSimple(jsonStr, DEFAULT_PUBLIC_KEY);
    }

    /**
     * 加密JSON数据（指定公钥）- 使用AES+RSA混合加密
     *
     * @param jsonStr 输入的JSON字符串
     * @param publicKeyStr 公钥字符串
     * @return 加密结果，格式：{"content": "加密后的十六进制字符串"}
     */
    public static String encryptJson(String jsonStr, String publicKeyStr) {
        try {
            log.info("输入JSON: {}", jsonStr);

            // 1. 生成AES密钥
            KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
            keyGenerator.init(256);
            SecretKey aesKey = keyGenerator.generateKey();

            // 2. 使用AES-GCM加密JSON数据
            Cipher aesCipher = Cipher.getInstance(AES_GCM_TRANSFORMATION);
            aesCipher.init(Cipher.ENCRYPT_MODE, aesKey);
            byte[] encryptedData = aesCipher.doFinal(jsonStr.getBytes(StandardCharsets.UTF_8));

            // 获取IV（初始化向量）
            byte[] iv = aesCipher.getIV();

            // 组合IV和加密数据
            byte[] encryptedWithIv = new byte[iv.length + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, iv.length);
            System.arraycopy(encryptedData, 0, encryptedWithIv, iv.length, encryptedData.length);

            // Base64编码加密后的内容
            String encryptedContent = Base64.encode(encryptedWithIv);

            // 3. 使用RSA-OAEP加密AES密钥
            PublicKey publicKey = loadPublicKey(publicKeyStr);
            Cipher rsaCipher = Cipher.getInstance(RSA_OAEP_TRANSFORMATION);
            rsaCipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encryptedAesKey = rsaCipher.doFinal(aesKey.getEncoded());
            String encryptedKey = Base64.encode(encryptedAesKey);

            // 4. 生成签名（简化处理）
            String signature = generateSignature(jsonStr, aesKey.getEncoded());

            String hexEncodedData = HexUtil.encodeHexStr(requestJson.getBytes(StandardCharsets.UTF_8));

            // 7. 构建返回结果
            Map<String, String> result = new HashMap<>();
            result.put("content", hexEncodedData);

            String resultJson = JSONUtil.toJsonPrettyStr(result);
            log.info("加密结果: {}", resultJson);

            return resultJson;

        } catch (Exception e) {
            log.error("JSON加密失败", e);
            Map<String, String> errorResult = new HashMap<>();
            errorResult.put("error", "加密失败: " + e.getMessage());
            return JSONUtil.toJsonPrettyStr(errorResult);
        }
    }

    /**
     * 生成签名（简化实现）
     */
    private static String generateSignature(String data, byte[] key) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(data.getBytes(StandardCharsets.UTF_8));
            digest.update(key);
            byte[] hash = digest.digest();
            return Base64.encode(hash);
        } catch (Exception e) {
            log.error("生成签名失败", e);
            return "";
        }
    }

    /**
     * 解密content数据
     * 
     * @param encryptedContent 加密的十六进制字符串
     * @param privateKeyStr 私钥字符串
     * @return 解密后的JSON字符串
     */
    public static String decryptContent(String encryptedContent, String privateKeyStr) {
        try {
            log.info("解密content: {}", encryptedContent);
            
            // 使用现有的解密方法
            String decryptedData = EncryptionUtil.decryptDataWithPrivateKey(encryptedContent, privateKeyStr);
            
            log.info("解密结果: {}", decryptedData);
            return decryptedData;
            
        } catch (Exception e) {
            log.error("解密失败", e);
            return "解密失败: " + e.getMessage();
        }
    }

    /**
     * 加载公钥
     */
    private static PublicKey loadPublicKey(String publicKeyStr) throws Exception {
        // 移除公钥头尾标识
        String publicKeyContent = publicKeyStr
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");
        
        // Base64解码
        byte[] keyBytes = Base64.decode(publicKeyContent);
        
        // 生成公钥
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 设置公钥
     */
    public static String encryptWithCustomKey(String jsonStr, String publicKey) {
        return encryptJson(jsonStr, publicKey);
    }

    /**
     * 主方法 - 用于快速测试
     */
    public static void main(String[] args) {
        // 测试不同的JSON数据
//        testCreateCard();
//        System.out.println();
//
//        testQueryCard();
//        System.out.println();
//
//        testRecharge();
//        System.out.println();
        
        // 自定义JSON测试
        String customJson = "{\"product_code\":\"VM-PREMIUM-2025\",\"first_name\":\"Wei\",\"last_name\":\"Zhang\",\"amount\":5000,\"single_limit\":2000,\"day_limit\":5000,\"month_limit\":30000,\"label\":\"Business Travel Card\",\"card_address\":{\"address_line_one\":\"18/F, Financial Tower\",\"address_line_two\":\"88 Finance Street\",\"city\":\"Shanghai\",\"state\":\"SH\",\"country\":\"CN\",\"post_code\":\"200120\"},\"area_code\":\"+86\",\"mobile\":\"***********\",\"email\":\"<EMAIL>\"} ";
        
        System.out.println("=== 自定义JSON加密 ===");
        String result = encryptJson(customJson);
        System.out.println(result);
    }
}
