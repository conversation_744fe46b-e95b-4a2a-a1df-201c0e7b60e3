package cn.iocoder.yudao.module.vm.util;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * VMCardIO API测试工具类
 * 
 * 用于测试VMCardIO的API接口，支持token获取和业务接口调用
 */
@Slf4j
public class VmCardIoApiTestUtil {

    // 测试环境配置
    private static final String BASE_URL = "https://sandbox.vmcardio.com";
    private static final String APP_ID = "your_app_id_here";
    private static final String APP_SECRET = "your_app_secret_here";
    private static final String PUBLIC_KEY = "your_public_key_here";
    private static final String PRIVATE_KEY = "your_private_key_here";
    
    // Token缓存
    private static String cachedToken = null;
    private static LocalDateTime tokenExpireTime = null;

    /**
     * Token响应结果
     */
    @Data
    public static class TokenResponse {
        private Integer code;
        private String message;
        private TokenData data;
        
        @Data
        public static class TokenData {
            private String token;
            private Integer expired_time;
        }
    }

    /**
     * API响应结果
     */
    @Data
    public static class ApiResponse {
        private Integer code;
        private String message;
        private Object data;
        private Boolean success;
    }

    /**
     * 获取访问令牌
     */
    public static String getAccessToken() {
        try {
            // 检查缓存的token是否有效
            if (isTokenValid()) {
                return cachedToken;
            }

            // 构建获取token的URL
            String url = BASE_URL + "/getAccessToken?app_id=" + APP_ID + "&app_secret=" + APP_SECRET;
            
            log.info("获取访问令牌: {}", url);
            
            // 发送GET请求
            HttpResponse response = HttpRequest.get(url)
                    .header("Content-Type", "application/json")
                    .timeout(30000)
                    .execute();

            if (!response.isOk()) {
                throw new RuntimeException("HTTP请求失败: " + response.getStatus());
            }

            // 解析响应
            String responseBody = response.body();
            log.info("Token响应: {}", responseBody);
            
            TokenResponse tokenResponse = JSONUtil.toBean(responseBody, TokenResponse.class);
            
            if (tokenResponse.getCode() == 0 && tokenResponse.getData() != null) {
                cachedToken = tokenResponse.getData().getToken();
                tokenExpireTime = LocalDateTimeUtil.of(tokenResponse.getData().getExpired_time());
                
                log.info("获取Token成功: {}, 过期时间: {}", cachedToken, tokenExpireTime);
                return cachedToken;
            } else {
                throw new RuntimeException("获取Token失败: " + tokenResponse.getMessage());
            }

        } catch (Exception e) {
            log.error("获取访问令牌失败", e);
            throw new RuntimeException("获取访问令牌失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查token是否有效
     */
    private static boolean isTokenValid() {
        return StrUtil.isNotBlank(cachedToken) && 
               tokenExpireTime != null && 
               LocalDateTime.now().isBefore(tokenExpireTime.minusMinutes(5));
    }

    /**
     * 调用业务API
     * 
     * @param endpoint API端点（如：/cards/create）
     * @param bizContentJson 业务内容的JSON字符串
     * @return API响应的JSON字符串
     */
    public static String callApi(String endpoint, String bizContentJson) {
        try {
            // 确保有有效的token
            String token = getAccessToken();
            
            // 解析业务内容
            Map<String, Object> bizContent = JSONUtil.toBean(bizContentJson, Map.class);
            
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("apiKey", APP_ID);
            requestData.put("timestamp", String.valueOf(System.currentTimeMillis()));
            requestData.put("rsaType", "ECB_OAEP");
            requestData.put("aesType", "GCM_NOPADDING");
            
            // 加密bizContent
            EncryptionUtil.EncryptionResult encryptionResult = EncryptionUtil.encryptBizContent(bizContent, PUBLIC_KEY);
            requestData.put("bizContent", encryptionResult.getEncryptedContent());
            requestData.put("key", encryptionResult.getEncryptedKey());
            requestData.put("sig", encryptionResult.getSignature());

            // 构建完整URL
            String url = BASE_URL + endpoint;
            
            log.info("调用API: {}", url);
            log.info("请求参数: {}", JSONUtil.toJsonPrettyStr(requestData));
            
            // 发送POST请求
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .header("Authorization", token)
                    .body(JSONUtil.toJsonStr(requestData))
                    .timeout(30000)
                    .execute();

            if (!response.isOk()) {
                throw new RuntimeException("HTTP请求失败: " + response.getStatus());
            }

            // 获取响应
            String responseBody = response.body();
            log.info("API响应: {}", responseBody);
            
            // 解析响应
            ApiResponse apiResponse = JSONUtil.toBean(responseBody, ApiResponse.class);
            
            if (apiResponse.getCode() == 0 && apiResponse.getData() != null) {
                // 解密响应数据
                String decryptedData = EncryptionUtil.decryptDataWithPrivateKey(
                        apiResponse.getData().toString(), PRIVATE_KEY);
                
                log.info("解密后的响应: {}", decryptedData);
                return decryptedData;
            } else {
                log.error("API调用失败: {}", apiResponse.getMessage());
                return JSONUtil.toJsonPrettyStr(apiResponse);
            }

        } catch (Exception e) {
            log.error("调用API失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return JSONUtil.toJsonPrettyStr(errorResponse);
        }
    }

    /**
     * 测试创建虚拟卡
     */
    public static String testCreateCard(String cardData) {
        return callApi("/cards/create", cardData);
    }

    /**
     * 测试查询虚拟卡
     */
    public static String testQueryCard(String queryData) {
        return callApi("/cards/query", queryData);
    }

    /**
     * 测试充值
     */
    public static String testRecharge(String rechargeData) {
        return callApi("/cards/recharge", rechargeData);
    }

    /**
     * 测试冻结卡片
     */
    public static String testFreezeCard(String freezeData) {
        return callApi("/cards/freeze", freezeData);
    }

    /**
     * 测试解冻卡片
     */
    public static String testUnfreezeCard(String unfreezeData) {
        return callApi("/cards/unfreeze", unfreezeData);
    }

    /**
     * 测试查询交易记录
     */
    public static String testQueryTransactions(String queryData) {
        return callApi("/transactions/query", queryData);
    }

    /**
     * 设置测试环境配置
     */
    public static void setConfig(String baseUrl, String appId, String appSecret, String publicKey, String privateKey) {
        // 这里可以动态设置配置，但为了简化，暂时使用常量
        log.info("配置已设置 - BaseURL: {}, AppID: {}", baseUrl, appId);
    }

    /**
     * 清除token缓存
     */
    public static void clearTokenCache() {
        cachedToken = null;
        tokenExpireTime = null;
        log.info("Token缓存已清除");
    }
}
