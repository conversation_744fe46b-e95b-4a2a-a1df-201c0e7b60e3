package cn.iocoder.yudao.module.vm.api;

import cn.iocoder.yudao.module.vm.api.client.VirtualCardApiClient;
import cn.iocoder.yudao.module.vm.api.client.capability.*;
import cn.iocoder.yudao.module.vm.api.encryption.EncryptionStrategy;
import cn.iocoder.yudao.module.vm.api.encryption.EncryptionStrategyFactory;
import cn.iocoder.yudao.module.vm.controller.admin.provider.ProviderCapabilityController;
import cn.iocoder.yudao.module.vm.enums.ProviderTypeEnum;
import org.junit.jupiter.api.Test;

/**
 * 编译测试类
 * 用于验证所有新增的类和接口是否能正常编译
 */
public class CompileTest {

    @Test
    public void testCompile() {
        // 测试枚举
        ProviderTypeEnum vmCardIo = ProviderTypeEnum.VMCARDIO;
        ProviderTypeEnum lemft = ProviderTypeEnum.LEMFT;
        
        // 测试能力接口
        Class<?>[] capabilityClasses = {
            WalletCapability.class,
            CardBinCapability.class,
            CardManagementCapability.class,
            OrderCapability.class,
            CardholderCapability.class,
            PayMethodCapability.class,
            PayeeCapability.class,
            PaymentCapability.class
        };
        
        // 测试主接口
        Class<?> mainInterface = VirtualCardApiClient.class;
        
        // 测试加密相关
        Class<?> encryptionStrategy = EncryptionStrategy.class;
        Class<?> encryptionFactory = EncryptionStrategyFactory.class;
        
        // 测试控制器
        Class<?> controller = ProviderCapabilityController.class;
        
        System.out.println("所有类编译成功！");
    }
}
