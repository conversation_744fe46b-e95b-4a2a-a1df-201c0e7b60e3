package cn.iocoder.yudao.module.vm.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * VMCardIO API测试用例
 */
@Slf4j
public class VmCardIoApiTest {

    @BeforeEach
    void setUp() {
        // 设置测试配置
        VmCardIoApiTestUtil.setConfig(
            "https://sandbox.vmcardio.com",
            "your_app_id_here",
            "your_app_secret_here", 
            "your_public_key_here",
            "your_private_key_here"
        );
        
        // 清除token缓存
        VmCardIoApiTestUtil.clearTokenCache();
    }

    @Test
    void testGetAccessToken() {
        log.info("=== 测试获取访问令牌 ===");
        
        try {
            String token = VmCardIoApiTestUtil.getAccessToken();
            log.info("获取到Token: {}", token);
        } catch (Exception e) {
            log.error("获取Token失败", e);
        }
    }

    @Test
    void testCreateCard() {
        log.info("=== 测试创建虚拟卡 ===");
        
        // 创建卡片的请求数据
        String createCardJson = """
            {
                "card_type": "VISA",
                "currency": "USD",
                "amount": 100.00,
                "holder_name": "John Doe",
                "mobile": "+1234567890",
                "email": "<EMAIL>",
                "label": "Test Card"
            }
            """;
        
        try {
            String response = VmCardIoApiTestUtil.testCreateCard(createCardJson);
            log.info("创建卡片响应: {}", response);
        } catch (Exception e) {
            log.error("创建卡片失败", e);
        }
    }

    @Test
    void testQueryCard() {
        log.info("=== 测试查询虚拟卡 ===");
        
        // 查询卡片的请求数据
        String queryCardJson = """
            {
                "page": 1,
                "pageSize": 10,
                "card_number": "****************"
            }
            """;
        
        try {
            String response = VmCardIoApiTestUtil.testQueryCard(queryCardJson);
            log.info("查询卡片响应: {}", response);
        } catch (Exception e) {
            log.error("查询卡片失败", e);
        }
    }

    @Test
    void testRecharge() {
        log.info("=== 测试充值 ===");
        
        // 充值的请求数据
        String rechargeJson = """
            {
                "card_id": "12345",
                "amount": 50.00,
                "currency": "USD",
                "description": "Test recharge"
            }
            """;
        
        try {
            String response = VmCardIoApiTestUtil.testRecharge(rechargeJson);
            log.info("充值响应: {}", response);
        } catch (Exception e) {
            log.error("充值失败", e);
        }
    }

    @Test
    void testFreezeCard() {
        log.info("=== 测试冻结卡片 ===");
        
        // 冻结卡片的请求数据
        String freezeJson = """
            {
                "card_id": "12345",
                "reason": "Security concern"
            }
            """;
        
        try {
            String response = VmCardIoApiTestUtil.testFreezeCard(freezeJson);
            log.info("冻结卡片响应: {}", response);
        } catch (Exception e) {
            log.error("冻结卡片失败", e);
        }
    }

    @Test
    void testUnfreezeCard() {
        log.info("=== 测试解冻卡片 ===");
        
        // 解冻卡片的请求数据
        String unfreezeJson = """
            {
                "card_id": "12345",
                "reason": "Issue resolved"
            }
            """;
        
        try {
            String response = VmCardIoApiTestUtil.testUnfreezeCard(unfreezeJson);
            log.info("解冻卡片响应: {}", response);
        } catch (Exception e) {
            log.error("解冻卡片失败", e);
        }
    }

    @Test
    void testQueryTransactions() {
        log.info("=== 测试查询交易记录 ===");
        
        // 查询交易记录的请求数据
        String queryTransactionsJson = """
            {
                "card_id": "12345",
                "page": 1,
                "pageSize": 20,
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
            }
            """;
        
        try {
            String response = VmCardIoApiTestUtil.testQueryTransactions(queryTransactionsJson);
            log.info("查询交易记录响应: {}", response);
        } catch (Exception e) {
            log.error("查询交易记录失败", e);
        }
    }

    /**
     * 自定义API调用测试
     */
    @Test
    void testCustomApiCall() {
        log.info("=== 测试自定义API调用 ===");
        
        // 自定义的请求数据
        String customJson = """
            {
                "custom_field": "custom_value",
                "test_data": true
            }
            """;
        
        try {
            String response = VmCardIoApiTestUtil.callApi("/custom/endpoint", customJson);
            log.info("自定义API响应: {}", response);
        } catch (Exception e) {
            log.error("自定义API调用失败", e);
        }
    }
}
