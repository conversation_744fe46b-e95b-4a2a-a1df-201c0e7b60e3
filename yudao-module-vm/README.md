# 虚拟卡管理系统

## 概述

虚拟卡管理系统是一个支持多厂商的虚拟卡业务管理平台，提供虚拟卡的创建、管理、充值、退款等功能，并确保本地数据与第三方厂商数据的一致性。

## 功能特性

### 核心功能
- **虚拟卡管理**: 创建、查询、更新、删除虚拟卡
- **卡片操作**: 冻结、解冻、充值、退款
- **多厂商支持**: 支持多个第三方虚拟卡厂商，可灵活切换
- **数据同步**: 定期同步第三方数据，确保数据一致性
- **交易记录**: 完整的交易历史记录和追踪

### 技术特性
- **策略模式**: 支持多厂商API客户端的灵活切换
- **数据加密**: 支持RSA加密与第三方API通信
- **异步处理**: 支持异步数据同步，提高系统性能
- **Webhook回调**: 接收第三方实时通知
- **定时任务**: 定期同步数据，确保一致性

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端管理界面   │    │   Controller层   │    │   Service层     │
│  (Vue3 Admin)   │◄──►│   (REST API)    │◄──►│  (业务逻辑)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   API客户端层   │    │   数据访问层     │
                       │ (多厂商支持)    │◄──►│   (MySQL)       │
                       └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   第三方API     │
                       │  (VMCardIO等)   │
                       └─────────────────┘
```

## 数据库设计

### 主要表结构

1. **vm_virtual_card**: 虚拟卡主表
2. **vm_provider_config**: 厂商配置表
3. **vm_transaction_record**: 交易记录表

## 快速开始

### 1. 数据库初始化

执行SQL脚本创建数据库表：
```sql
-- 执行 src/main/resources/sql/vm_tables.sql
```

### 2. 配置厂商信息

在管理后台配置厂商信息：
- 厂商类型
- API基础URL
- API Key
- 公钥/私钥（用于加密通信）

### 3. 创建虚拟卡

通过管理后台或API创建虚拟卡：
```http
POST /admin-api/vm/virtual-card/create
{
  "productCode": "Q493875YK",
  "firstName": "张",
  "lastName": "三",
  "amount": 100.00,
  "email": "<EMAIL>",
  "mobile": "13800138000"
}
```

## API接口

### 虚拟卡管理
- `POST /vm/virtual-card/create` - 创建虚拟卡
- `GET /vm/virtual-card/get` - 获取虚拟卡详情
- `GET /vm/virtual-card/page` - 分页查询虚拟卡
- `PUT /vm/virtual-card/update` - 更新虚拟卡
- `DELETE /vm/virtual-card/delete` - 删除虚拟卡

### 卡片操作
- `POST /vm/virtual-card/freeze` - 冻结虚拟卡
- `POST /vm/virtual-card/unfreeze` - 解冻虚拟卡
- `POST /vm/virtual-card/recharge` - 充值虚拟卡
- `POST /vm/virtual-card/refund` - 退款虚拟卡

### 厂商配置
- `POST /vm/provider-config/create` - 创建厂商配置
- `GET /vm/provider-config/page` - 分页查询厂商配置
- `PUT /vm/provider-config/update` - 更新厂商配置
- `POST /vm/provider-config/set-default` - 设置默认厂商

### 交易记录
- `GET /vm/transaction-record/page` - 分页查询交易记录
- `GET /vm/transaction-record/list-by-card` - 根据卡片查询交易记录

### 系统管理
- `POST /vm/management/sync/all` - 同步所有虚拟卡数据
- `GET /vm/management/check-consistency` - 检查数据一致性
- `POST /vm/management/fix-inconsistency` - 修复数据不一致

## 多厂商支持架构

### 架构设计

本系统采用**能力组合模式**设计，支持不同厂商实现不同的功能能力：

#### 核心接口层次
```
VirtualCardApiClient (主接口)
├── BaseVirtualCardApiClient (基础能力 - 必须实现)
│   ├── 创建虚拟卡
│   ├── 获取卡片详情
│   ├── 冻结/解冻卡片
│   └── 删除卡片
├── AccountCapability (账户管理能力 - 可选)
│   ├── 获取账户余额
│   ├── 获取钱包列表
│   └── 获取钱包详情
├── CardManagementCapability (卡片管理能力 - 可选)
│   ├── 获取卡产品列表
│   ├── 卡Bin管理
│   ├── 卡激活
│   └── 修改卡限额
├── TransactionCapability (交易能力 - 可选)
│   ├── 卡片充值
│   ├── 卡片退款
│   └── 交易记录查询
├── CardholderCapability (持卡人管理能力 - 可选)
│   ├── 创建/编辑持卡人
│   ├── 持卡人列表
│   └── 删除持卡人
└── PaymentCapability (付款能力 - 可选)
    ├── 汇率查询
    ├── 收款方管理
    └── 付款功能
```

#### 加密策略支持
不同厂商使用不同的加密方式：
- **VMCardIO**: RSA加密 + PKCS1Padding + Base64 + 十六进制编码
- **LEMFT**: AES加密（示例，根据实际API调整）

### 添加新厂商

1. **添加厂商枚举**:
```java
// 在 ProviderTypeEnum 中添加
NEW_PROVIDER(3, "new_provider", "新厂商", "https://api.newprovider.com/");
```

2. **实现API客户端**:
```java
@Component
public class NewProviderApiClient implements VirtualCardApiClient {
    @Override
    public Integer getProviderType() {
        return ProviderTypeEnum.NEW_PROVIDER.getType();
    }

    // 实现基础能力（必须）
    @Override
    public ApiResponseDTO<CreateCardResponseDTO> createCard(CreateCardRequestDTO request) {
        // 实现创建卡片逻辑
    }

    // 实现可选能力（根据厂商支持情况）
    @Override
    public ApiResponseDTO<AccountBalanceResponseDTO> getAccountBalance() {
        // 如果厂商支持，实现获取余额逻辑
    }

    // 不支持的功能使用默认实现（抛出UnsupportedOperationException）
}
```

3. **实现加密策略（如果需要）**:
```java
@Component
public class NewProviderEncryptionStrategy implements EncryptionStrategy {
    @Override
    public String encrypt(String data, String publicKey) {
        // 实现厂商特定的加密逻辑
    }

    @Override
    public String decrypt(String encryptedData, String privateKey) {
        // 实现厂商特定的解密逻辑
    }

    @Override
    public String getEncryptionType() {
        return "NEW_PROVIDER_ENCRYPTION";
    }
}
```

4. **更新加密工厂**:
```java
// 在 EncryptionStrategyFactory.getProviderTypeByEncryptionType() 中添加
case "NEW_PROVIDER_ENCRYPTION":
    return ProviderTypeEnum.NEW_PROVIDER.getType();
```

### 厂商能力查询

系统提供API查询各厂商支持的能力：

```http
GET /vm/provider-capability/list
```

响应示例：
```json
{
  "code": 0,
  "data": [
    {
      "providerType": 1,
      "providerName": "VMCardIO",
      "providerCode": "vmcardio",
      "available": true,
      "hasAccountCapability": true,
      "hasCardManagementCapability": true,
      "hasTransactionCapability": true,
      "hasCardholderCapability": false,
      "hasPaymentCapability": false,
      "supportedFeatures": [
        "创建虚拟卡",
        "获取卡片详情",
        "冻结卡片",
        "解冻卡片",
        "删除卡片",
        "获取账户余额",
        "获取卡产品",
        "卡片充值",
        "卡片退款",
        "交易记录查询"
      ]
    },
    {
      "providerType": 2,
      "providerName": "LEMFT",
      "providerCode": "lemft",
      "available": true,
      "hasAccountCapability": true,
      "hasCardManagementCapability": true,
      "hasTransactionCapability": true,
      "hasCardholderCapability": true,
      "hasPaymentCapability": true,
      "supportedFeatures": [
        "创建虚拟卡",
        "获取卡片详情",
        "冻结卡片",
        "解冻卡片",
        "删除卡片",
        "钱包管理",
        "卡Bin管理",
        "持卡人管理",
        "汇率查询",
        "收款方管理",
        "付款功能"
      ]
    }
  ]
}
```

### 优势

1. **灵活扩展**: 新厂商只需实现支持的能力接口
2. **功能隔离**: 不同能力独立实现，互不影响
3. **向后兼容**: 新增能力不影响现有厂商
4. **统一管理**: 通过工厂模式统一管理所有厂商
5. **加密解耦**: 加密策略独立，支持不同厂商的加密方式
```

2. **添加厂商枚举**:
```java
NEW_PROVIDER(4, "new_provider", "新厂商", "https://api.new-provider.com/");
```

3. **配置厂商信息**: 在管理后台添加厂商配置

### 厂商切换

系统支持动态切换厂商：
- 设置默认厂商
- 为特定操作指定厂商
- 支持多厂商并存

## 数据同步

### 自动同步
- 定时任务：每小时同步一次
- Webhook回调：实时接收第三方通知
- 手动同步：支持手动触发同步

### 一致性保证
- 数据校验：定期检查本地与第三方数据一致性
- 自动修复：发现不一致时自动修复
- 冲突处理：以第三方数据为准

## 安全特性

### 数据加密
- RSA加密：与第三方API通信使用RSA加密
- 敏感信息：卡号、CVV等敏感信息加密存储

### 访问控制
- 权限管理：基于角色的访问控制
- IP白名单：限制API访问来源
- 操作日志：记录所有操作日志

## 监控告警

### 系统监控
- API调用监控
- 数据同步状态监控
- 系统健康检查

### 告警机制
- 同步失败告警
- 数据不一致告警
- API调用异常告警

## 部署说明

### 环境要求
- JDK 8+
- MySQL 5.7+
- Redis 3.0+

### 配置项
```yaml
vm:
  sync:
    enabled: true
    interval: 3600000  # 同步间隔（毫秒）
  webhook:
    enabled: true
    path: /vm/webhook
```

## 常见问题

### Q: 如何添加新的厂商？
A: 实现VirtualCardApiClient接口，添加厂商枚举，配置厂商信息即可。

### Q: 数据不一致怎么办？
A: 使用数据一致性检查功能，系统会自动检测并修复不一致的数据。

### Q: 如何确保数据安全？
A: 系统使用RSA加密与第三方通信，敏感数据加密存储，并提供完整的权限控制。

## 技术支持

如有问题，请联系技术支持团队或查看详细的API文档。
