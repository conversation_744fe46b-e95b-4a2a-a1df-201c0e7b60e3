# 私钥格式问题排查指南

## 错误现象

```
java.security.InvalidKeyException: IOException : DerInputStream.getLength(): lengthTag=111, too big.
```

## 问题原因

这个错误通常由以下原因引起：

1. **私钥格式不正确**: 私钥可能是PKCS#1格式，但代码期望PKCS#8格式
2. **私钥内容损坏**: Base64编码有误或内容不完整
3. **私钥头尾标识错误**: 缺少或错误的BEGIN/END标识
4. **编码问题**: 私钥包含非法字符或编码错误

## 解决方案

### 1. 检查私钥格式

#### PKCS#1 格式 (传统RSA格式)
```
-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA...
-----END RSA PRIVATE KEY-----
```

#### PKCS#8 格式 (标准格式)
```
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
-----END PRIVATE KEY-----
```

### 2. 使用工具类检测格式

```java
String format = RSAKeyUtil.detectPrivateKeyFormat(privateKeyString);
System.out.println("私钥格式: " + format);
```

### 3. 格式转换

#### 从PKCS#1转换为PKCS#8
```java
String pkcs1Key = "-----BEGIN RSA PRIVATE KEY-----\n...";
String pkcs8Key = PrivateKeyConverter.convertPKCS1ToPKCS8(pkcs1Key);
```

#### 从PKCS#8转换为PKCS#1
```java
String pkcs8Key = "-----BEGIN PRIVATE KEY-----\n...";
String pkcs1Key = PrivateKeyConverter.convertPKCS8ToPKCS1(pkcs8Key);
```

### 4. 使用OpenSSL转换

#### PKCS#1转PKCS#8
```bash
openssl pkcs8 -topk8 -inform PEM -outform PEM -nocrypt -in pkcs1_key.pem -out pkcs8_key.pem
```

#### PKCS#8转PKCS#1
```bash
openssl rsa -in pkcs8_key.pem -out pkcs1_key.pem
```

## 常见问题排查

### 1. 检查私钥完整性

```java
public static boolean validatePrivateKeyFormat(String privateKey) {
    try {
        // 清理私钥
        String cleaned = privateKey
            .replace("-----BEGIN PRIVATE KEY-----", "")
            .replace("-----END PRIVATE KEY-----", "")
            .replace("-----BEGIN RSA PRIVATE KEY-----", "")
            .replace("-----END RSA PRIVATE KEY-----", "")
            .replaceAll("\\s", "");
        
        // 检查是否为有效的Base64
        byte[] decoded = Base64.decode(cleaned);
        return decoded.length > 0;
    } catch (Exception e) {
        return false;
    }
}
```

### 2. 验证私钥可用性

```java
public static void testPrivateKey(String privateKeyStr) {
    try {
        PrivateKey privateKey = RSAKeyUtil.loadPrivateKey(privateKeyStr);
        System.out.println("✓ 私钥加载成功");
        System.out.println("  算法: " + privateKey.getAlgorithm());
        System.out.println("  格式: " + privateKey.getFormat());
        
        // 测试签名功能
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update("test".getBytes());
        byte[] signed = signature.sign();
        System.out.println("✓ 私钥签名功能正常");
        
    } catch (Exception e) {
        System.err.println("✗ 私钥测试失败: " + e.getMessage());
        e.printStackTrace();
    }
}
```

### 3. 生成测试密钥对

```bash
# 生成PKCS#1格式私钥
openssl genrsa -out private_pkcs1.pem 2048

# 转换为PKCS#8格式
openssl pkcs8 -topk8 -inform PEM -outform PEM -nocrypt -in private_pkcs1.pem -out private_pkcs8.pem

# 生成对应的公钥
openssl rsa -in private_pkcs8.pem -pubout -out public.pem
```

## 最佳实践

### 1. 统一使用PKCS#8格式

- 现代应用推荐使用PKCS#8格式
- Java默认支持PKCS#8格式
- 更好的跨平台兼容性

### 2. 私钥存储建议

```java
// 推荐的私钥存储格式
String privateKey = """
    -----BEGIN PRIVATE KEY-----
    MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
    (每行64个字符)
    ...
    -----END PRIVATE KEY-----
    """;
```

### 3. 错误处理

```java
public PrivateKey loadPrivateKeySafely(String privateKeyStr) {
    try {
        return RSAKeyUtil.loadPrivateKey(privateKeyStr);
    } catch (Exception e) {
        log.error("私钥加载失败，尝试格式转换");
        
        // 尝试检测并转换格式
        String format = RSAKeyUtil.detectPrivateKeyFormat(privateKeyStr);
        if ("PKCS1".equals(format)) {
            try {
                String pkcs8Key = PrivateKeyConverter.convertPKCS1ToPKCS8(privateKeyStr);
                return RSAKeyUtil.loadPrivateKey(pkcs8Key);
            } catch (Exception e2) {
                log.error("格式转换失败", e2);
            }
        }
        
        throw new RuntimeException("私钥格式不支持或已损坏", e);
    }
}
```

## 调试步骤

1. **确认私钥格式**
   ```java
   String format = RSAKeyUtil.detectPrivateKeyFormat(privateKey);
   ```

2. **验证Base64编码**
   ```java
   boolean valid = validatePrivateKeyFormat(privateKey);
   ```

3. **尝试格式转换**
   ```java
   String converted = PrivateKeyConverter.convertPKCS1ToPKCS8(privateKey);
   ```

4. **测试加载**
   ```java
   testPrivateKey(privateKey);
   ```

## 联系支持

如果以上方法都无法解决问题，请提供：

1. 私钥的生成方式
2. 私钥的格式类型
3. 完整的错误堆栈信息
4. 使用的Java版本和操作系统

**注意**: 请勿在支持请求中包含实际的私钥内容！
