import request from '@/config/axios'

export interface SystemStatusVO {
  supportedProviders: number[]
  apiClientsLoaded: number
  systemHealthy: boolean
}

// 同步所有虚拟卡数据
export const syncAllVirtualCards = () => {
  return request.post({ url: '/vm/management/sync/all' })
}

// 同步指定厂商的虚拟卡数据
export const syncVirtualCardsByProvider = (providerType: number) => {
  return request.post({ url: '/vm/management/sync/provider?providerType=' + providerType })
}

// 同步单个虚拟卡数据
export const syncVirtualCard = (cardId: number) => {
  return request.post({ url: '/vm/management/sync/card?cardId=' + cardId })
}

// 检查数据一致性
export const checkDataConsistency = (): Promise<string> => {
  return request.get({ url: '/vm/management/check-consistency' })
}

// 修复数据不一致问题
export const fixDataInconsistency = (autoFix: boolean): Promise<string> => {
  return request.post({ url: '/vm/management/fix-inconsistency?autoFix=' + autoFix })
}

// 刷新API客户端
export const refreshApiClients = () => {
  return request.post({ url: '/vm/management/refresh-api-clients' })
}

// 获取支持的厂商类型
export const getSupportedProviders = (): Promise<number[]> => {
  return request.get({ url: '/vm/management/supported-providers' })
}

// 检查厂商是否支持
export const checkProviderSupport = (providerType: number): Promise<boolean> => {
  return request.get({ url: '/vm/management/provider-support?providerType=' + providerType })
}

// 获取系统状态
export const getSystemStatus = (): Promise<SystemStatusVO> => {
  return request.get({ url: '/vm/management/system-status' })
}
