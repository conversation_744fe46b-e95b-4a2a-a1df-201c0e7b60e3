import request from '@/config/axios'
import type { PageParam } from '@/api/common'

export interface ProviderConfigVO {
  id: number
  providerType: number
  providerName: string
  baseUrl: string
  apiKey: string
  publicKey: string
  privateKey: string
  enabled: boolean
  isDefault: boolean
  timeout: number
  retryCount: number
  ipWhitelist: string
  extConfig: string
  createTime: Date
  remark: string
}

export interface ProviderConfigSaveReqVO {
  id?: number
  providerType: number
  providerName: string
  baseUrl: string
  apiKey: string
  publicKey?: string
  privateKey?: string
  enabled: boolean
  isDefault: boolean
  timeout: number
  retryCount: number
  ipWhitelist?: string
  extConfig?: string
  remark?: string
}

export interface ProviderConfigPageReqVO extends PageParam {
  providerName?: string
  providerType?: number
  enabled?: boolean
  createTime?: Date[]
}



// 查询厂商配置分页
export const getProviderConfigPage = (params: ProviderConfigPageReqVO) => {
  return request.get({ url: '/vm/provider-config/page', params })
}

// 查询厂商配置详情
export const getProviderConfig = (id: number) => {
  return request.get({ url: '/vm/provider-config/get?id=' + id })
}

// 新增厂商配置
export const createProviderConfig = (data: ProviderConfigSaveReqVO) => {
  return request.post({ url: '/vm/provider-config/create', data })
}

// 修改厂商配置
export const updateProviderConfig = (data: ProviderConfigSaveReqVO) => {
  return request.put({ url: '/vm/provider-config/update', data })
}

// 删除厂商配置
export const deleteProviderConfig = (id: number) => {
  return request.delete({ url: '/vm/provider-config/delete?id=' + id })
}

// 导出厂商配置
export const exportProviderConfig = (params: any) => {
  return request.download({ url: '/vm/provider-config/export-excel', params })
}

// 获取启用的厂商配置列表
export const getEnabledProviderList = (): Promise<ProviderConfigVO[]> => {
  return request.get({ url: '/vm/provider-config/list-enabled' })
}

// 设置默认厂商
export const setDefaultProvider = (id: number) => {
  return request.post({ url: '/vm/provider-config/set-default?id=' + id })
}

// 更新厂商状态
export const updateProviderStatus = (id: number, enabled: boolean) => {
  return request.post({ url: '/vm/provider-config/update-status?id=' + id + '&enabled=' + enabled })
}

// 厂商能力相关接口
export interface ProviderCapabilityVO {
  providerType: number
  providerName: string
  providerCode: string
  available: boolean
  errorMessage?: string
  hasAccountCapability: boolean
  hasCardManagementCapability: boolean
  hasTransactionCapability: boolean
  hasCardholderCapability: boolean
  hasPaymentCapability: boolean
  supportedFeatures: string[]
}

// 获取厂商能力列表
export const getProviderCapabilities = (): Promise<ProviderCapabilityVO[]> => {
  return request.get({ url: '/vm/provider-capability/list' })
}

