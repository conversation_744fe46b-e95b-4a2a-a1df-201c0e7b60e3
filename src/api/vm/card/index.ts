import request from '@/config/axios'

export interface VirtualCardVO {
  id: number
  thirdPartyCardId: string
  cardNumber: string
  cvv: string
  expireDate: string
  holderName: string
  firstName: string
  lastName: string
  availableAmount: number
  cardType: string
  status: number
  statusName: string
  providerType: number
  providerName: string
  productCode: string
  label: string
  areaCode: string
  mobile: string
  email: string
  cardAddress: string
  thirdPartyCreateTime: Date
  lastSyncTime: Date
  createTime: Date
  remark: string
}

export interface VirtualCardCreateReqVO {
  productCode: string
  firstName: string
  lastName: string
  amount: number
  label?: string
  areaCode?: string
  mobile?: string
  email?: string
  cardAddress?: string
  providerType?: number
  remark?: string
}

export interface VirtualCardUpdateReqVO {
  id: number
  label?: string
  areaCode?: string
  mobile?: string
  email?: string
  cardAddress?: string
  remark?: string
}

export interface VirtualCardPageReqVO extends PageParam {
  cardNumber?: string
  holderName?: string
  status?: number
  providerType?: number
  label?: string
  createTime?: Date[]
}

// 查询虚拟卡分页
export const getVirtualCardPage = (params: VirtualCardPageReqVO) => {
  return request.get({ url: '/vm/virtual-card/page', params })
}

// 查询虚拟卡详情
export const getVirtualCard = (id: number) => {
  return request.get({ url: '/vm/virtual-card/get?id=' + id })
}

// 新增虚拟卡
export const createVirtualCard = (data: VirtualCardCreateReqVO) => {
  return request.post({ url: '/vm/virtual-card/create', data })
}

// 修改虚拟卡
export const updateVirtualCard = (data: VirtualCardUpdateReqVO) => {
  return request.put({ url: '/vm/virtual-card/update', data })
}

// 删除虚拟卡
export const deleteVirtualCard = (id: number) => {
  return request.delete({ url: '/vm/virtual-card/delete?id=' + id })
}

// 导出虚拟卡
export const exportVirtualCard = (params: any) => {
  return request.download({ url: '/vm/virtual-card/export-excel', params })
}

// 冻结虚拟卡
export const freezeVirtualCard = (id: number) => {
  return request.post({ url: '/vm/virtual-card/freeze?id=' + id })
}

// 解冻虚拟卡
export const unfreezeVirtualCard = (id: number) => {
  return request.post({ url: '/vm/virtual-card/unfreeze?id=' + id })
}

// 充值虚拟卡
export const rechargeVirtualCard = (id: number, amount: number) => {
  return request.post({ url: '/vm/virtual-card/recharge?id=' + id + '&amount=' + amount })
}

// 退款虚拟卡
export const refundVirtualCard = (id: number, amount: number) => {
  return request.post({ url: '/vm/virtual-card/refund?id=' + id + '&amount=' + amount })
}

// 同步虚拟卡信息
export const syncVirtualCard = (id: number) => {
  return request.post({ url: '/vm/virtual-card/sync?id=' + id })
}

// 批量同步虚拟卡信息
export const batchSyncVirtualCards = (providerType?: number) => {
  const params = providerType ? { providerType } : {}
  return request.post({ url: '/vm/virtual-card/batch-sync', params })
}
