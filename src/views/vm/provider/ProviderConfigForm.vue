<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="厂商类型" prop="providerType">
            <el-select v-model="formData.providerType" placeholder="请选择厂商类型" class="w-full">
              <el-option
                v-for="dict in getIntDictOptions('vm_provider_type')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="厂商名称" prop="providerName">
            <el-input v-model="formData.providerName" placeholder="请输入厂商名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="API基础URL" prop="baseUrl">
        <el-input v-model="formData.baseUrl" placeholder="请输入API基础URL" />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="App ID" prop="appId">
            <el-input v-model="formData.appId" placeholder="请输入App ID" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="App 密钥" prop="appSecret">
            <el-input v-model="formData.appSecret" placeholder="请输入App 密钥" show-password />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="厂商公钥" prop="publicKey">
        <el-input
          v-model="formData.publicKey"
          type="textarea"
          :rows="4"
          placeholder="请输入厂商公钥（用于加密请求给厂商）"
        />
      </el-form-item>

      <el-form-item label="我自己的私钥" prop="privateKey">
        <el-input
          v-model="formData.privateKey"
          type="textarea"
          :rows="4"
          placeholder="请输入我自己的私钥（用于解密厂商响应）"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="启用状态" prop="enabled">
            <el-switch v-model="formData.enabled" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="默认厂商" prop="isDefault">
            <el-switch v-model="formData.isDefault" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="超时时间(秒)" prop="timeout">
            <el-input-number v-model="formData.timeout" :min="1" :max="300" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="重试次数" prop="retryCount">
            <el-input-number v-model="formData.retryCount" :min="0" :max="10" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="IP白名单" prop="ipWhitelist">
            <el-input v-model="formData.ipWhitelist" placeholder="多个IP用逗号分隔" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="扩展配置" prop="extConfig">
        <el-input
          v-model="formData.extConfig"
          type="textarea"
          :rows="3"
          placeholder="请输入扩展配置（JSON格式）"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { getIntDictOptions } from '@/utils/dict'
import * as ProviderConfigApi from '@/api/vm/provider'

defineOptions({ name: 'ProviderConfigForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  providerType: undefined,
  providerName: '',
  baseUrl: '',
  appId: '',
  appSecret: '',
  publicKey: '',
  privateKey: '',
  enabled: true,
  isDefault: false,
  timeout: 30,
  retryCount: 3,
  ipWhitelist: '',
  extConfig: '',
  remark: ''
})
const formRules = reactive({
  providerType: [{ required: true, message: '厂商类型不能为空', trigger: 'change' }],
  providerName: [{ required: true, message: '厂商名称不能为空', trigger: 'blur' }],
  baseUrl: [
    { required: true, message: 'API基础URL不能为空', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  appId: [{ required: true, message: 'App ID不能为空', trigger: 'blur' }],
  appSecret: [{ required: true, message: 'App 密钥不能为空', trigger: 'blur' }],
  enabled: [{ required: true, message: '启用状态不能为空', trigger: 'change' }],
  isDefault: [{ required: true, message: '默认厂商不能为空', trigger: 'change' }],
  timeout: [{ required: true, message: '超时时间不能为空', trigger: 'blur' }],
  retryCount: [{ required: true, message: '重试次数不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref


/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '添加厂商配置' : '修改厂商配置'
  formType.value = type
  resetForm()
  
  // 厂商类型使用字典数据，无需额外加载
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await ProviderConfigApi.getProviderConfig(id)
      formData.value = data
    } finally {
      formLoading.value = false
    }
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value?.validate()
  
  // 校验扩展配置JSON格式
  if (formData.value.extConfig) {
    try {
      JSON.parse(formData.value.extConfig)
    } catch {
      message.error('扩展配置必须是有效的JSON格式')
      return
    }
  }
  
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ProviderConfigApi.ProviderConfigSaveReqVO
    if (formType.value === 'create') {
      await ProviderConfigApi.createProviderConfig(data)
      message.success(t('common.createSuccess'))
    } else {
      await ProviderConfigApi.updateProviderConfig(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    providerType: undefined,
    providerName: '',
    baseUrl: '',
    appId: '',
    appSecret: '',
    publicKey: '',
    privateKey: '',
    enabled: true,
    isDefault: false,
    timeout: 30,
    retryCount: 3,
    ipWhitelist: '',
    extConfig: '',
    remark: ''
  }
  formRef.value?.resetFields()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
