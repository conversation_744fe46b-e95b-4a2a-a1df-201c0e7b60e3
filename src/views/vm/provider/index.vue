<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="厂商名称" prop="providerName">
        <el-input
          v-model="queryParams.providerName"
          placeholder="请输入厂商名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="厂商类型" prop="providerType">
        <el-select
          v-model="queryParams.providerType"
          placeholder="请选择厂商类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="provider in providerTypes"
            :key="provider.type"
            :label="provider.name"
            :value="provider.type"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="启用状态" prop="enabled">
        <el-select
          v-model="queryParams.enabled"
          placeholder="请选择启用状态"
          clearable
          class="!w-240px"
        >
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" />搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vm:provider-config:create']"
        >
          <Icon icon="ep:plus" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vm:provider-config:export']"
        >
          <Icon icon="ep:download" />导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" stripe>
      <el-table-column label="厂商ID" align="center" prop="id" width="80" />
      <el-table-column label="厂商名称" align="center" prop="providerName" width="150" />
      <el-table-column label="厂商类型" align="center" prop="providerType" width="100">
        <template #default="scope">
          <dict-tag type="vm_provider_type" :value="scope.row.providerType" />
        </template>
      </el-table-column>
      <el-table-column label="API基础URL" align="center" prop="baseUrl" width="200" />
      <el-table-column label="启用状态" align="center" prop="enabled" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.enabled"
            @change="handleStatusChange(scope.row)"
            v-hasPermi="['vm:provider-config:update']"
          />
        </template>
      </el-table-column>
      <el-table-column label="默认厂商" align="center" prop="isDefault" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.isDefault" type="success">是</el-tag>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column label="超时时间" align="center" prop="timeout" width="100">
        <template #default="scope">
          {{ scope.row.timeout }}秒
        </template>
      </el-table-column>
      <el-table-column label="重试次数" align="center" prop="retryCount" width="100" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180"
      />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <div class="flex items-center justify-center">
            <el-button
              type="primary"
              link
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['vm:provider-config:update']"
            >
              <Icon icon="ep:edit" />修改
            </el-button>
            <el-button
              type="success"
              link
              @click="handleSetDefault(scope.row)"
              v-if="!scope.row.isDefault"
              v-hasPermi="['vm:provider-config:update']"
            >
              <Icon icon="ep:star" />设为默认
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(scope.row.id)"
              v-hasPermi="['vm:provider-config:delete']"
            >
              <Icon icon="ep:delete" />删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ProviderConfigForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">

import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as ProviderConfigApi from '@/api/vm/provider'
import ProviderConfigForm from './ProviderConfigForm.vue'

defineOptions({ name: 'VmProviderConfig' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  providerName: undefined,
  providerType: undefined,
  enabled: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中


/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProviderConfigApi.getProviderConfigPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await ProviderConfigApi.deleteProviderConfig(id)
    message.success(t('common.delSuccess'))
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    exportLoading.value = true
    const data = await ProviderConfigApi.exportProviderConfig(queryParams)
    download.excel(data, '厂商配置数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 状态变更操作 */
const handleStatusChange = async (row: ProviderConfigApi.ProviderConfigVO) => {
  try {
    await ProviderConfigApi.updateProviderStatus(row.id, row.enabled)
    message.success('状态更新成功')
  } catch {
    // 恢复原状态
    row.enabled = !row.enabled
  }
}

/** 设为默认厂商 */
const handleSetDefault = async (row: ProviderConfigApi.ProviderConfigVO) => {
  try {
    await message.confirm(`确认要将"${row.providerName}"设为默认厂商吗？`)
    await ProviderConfigApi.setDefaultProvider(row.id)
    message.success('设置成功')
    await getList()
  } catch {}
}

/** 初始化 */
onMounted(async () => {
  await getList()
  // 厂商类型使用字典数据，无需额外加载
})
</script>
