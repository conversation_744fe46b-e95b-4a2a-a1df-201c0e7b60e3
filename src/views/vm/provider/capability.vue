<template>
  <ContentWrap>
    <!-- 页面标题 -->
    <div class="mb-4">
      <h2 class="text-xl font-bold">厂商能力对比</h2>
      <p class="text-gray-600 mt-2">查看各厂商支持的功能能力，帮助您选择合适的厂商</p>
    </div>

    <!-- 能力对比表格 -->
    <el-table 
      :data="capabilityList" 
      v-loading="loading"
      border
      style="width: 100%"
      class="capability-table"
    >
      <el-table-column prop="providerName" label="厂商名称" width="120" fixed="left">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-tag 
              :type="row.available ? 'success' : 'danger'" 
              size="small"
              class="mr-2"
            >
              {{ row.available ? '可用' : '不可用' }}
            </el-tag>
            <span class="font-medium">{{ row.providerName }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="核心能力" width="600">
        <el-table-column prop="hasWalletCapability" label="钱包管理" width="100" align="center">
          <template #default="{ row }">
            <el-icon :color="row.hasWalletCapability ? '#67C23A' : '#F56C6C'">
              <component :is="row.hasWalletCapability ? 'Check' : 'Close'" />
            </el-icon>
          </template>
        </el-table-column>

        <el-table-column prop="hasCardBinCapability" label="卡Bin管理" width="100" align="center">
          <template #default="{ row }">
            <el-icon :color="row.hasCardBinCapability ? '#67C23A' : '#F56C6C'">
              <component :is="row.hasCardBinCapability ? 'Check' : 'Close'" />
            </el-icon>
          </template>
        </el-table-column>

        <el-table-column prop="hasCardManagementCapability" label="卡片管理" width="100" align="center">
          <template #default="{ row }">
            <el-icon :color="row.hasCardManagementCapability ? '#67C23A' : '#F56C6C'">
              <component :is="row.hasCardManagementCapability ? 'Check' : 'Close'" />
            </el-icon>
          </template>
        </el-table-column>

        <el-table-column prop="hasOrderCapability" label="订单管理" width="100" align="center">
          <template #default="{ row }">
            <el-icon :color="row.hasOrderCapability ? '#67C23A' : '#F56C6C'">
              <component :is="row.hasOrderCapability ? 'Check' : 'Close'" />
            </el-icon>
          </template>
        </el-table-column>

        <el-table-column prop="hasCardholderCapability" label="持卡人管理" width="100" align="center">
          <template #default="{ row }">
            <el-icon :color="row.hasCardholderCapability ? '#67C23A' : '#F56C6C'">
              <component :is="row.hasCardholderCapability ? 'Check' : 'Close'" />
            </el-icon>
          </template>
        </el-table-column>

        <el-table-column prop="hasPayeeCapability" label="收款方管理" width="100" align="center">
          <template #default="{ row }">
            <el-icon :color="row.hasPayeeCapability ? '#67C23A' : '#F56C6C'">
              <component :is="row.hasPayeeCapability ? 'Check' : 'Close'" />
            </el-icon>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button 
            type="primary" 
            size="small" 
            @click="showFeatureDetail(row)"
            :disabled="!row.available"
          >
            查看详情
          </el-button>
          <el-button 
            type="info" 
            size="small" 
            @click="showErrorDetail(row)"
            v-if="!row.available"
          >
            查看错误
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 功能详情对话框 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      :title="`${selectedProvider?.providerName} - 支持的功能`"
      width="600px"
    >
      <div v-if="selectedProvider">
        <!-- 能力分类 -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-3">能力分类</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="capability-item">
              <el-icon :color="selectedProvider.hasWalletCapability ? '#67C23A' : '#F56C6C'" class="mr-2">
                <component :is="selectedProvider.hasWalletCapability ? 'Check' : 'Close'" />
              </el-icon>
              <span>钱包管理</span>
            </div>
            <div class="capability-item">
              <el-icon :color="selectedProvider.hasCardBinCapability ? '#67C23A' : '#F56C6C'" class="mr-2">
                <component :is="selectedProvider.hasCardBinCapability ? 'Check' : 'Close'" />
              </el-icon>
              <span>卡Bin管理</span>
            </div>
            <div class="capability-item">
              <el-icon :color="selectedProvider.hasCardManagementCapability ? '#67C23A' : '#F56C6C'" class="mr-2">
                <component :is="selectedProvider.hasCardManagementCapability ? 'Check' : 'Close'" />
              </el-icon>
              <span>卡片管理</span>
            </div>
            <div class="capability-item">
              <el-icon :color="selectedProvider.hasOrderCapability ? '#67C23A' : '#F56C6C'" class="mr-2">
                <component :is="selectedProvider.hasOrderCapability ? 'Check' : 'Close'" />
              </el-icon>
              <span>订单管理</span>
            </div>
            <div class="capability-item">
              <el-icon :color="selectedProvider.hasCardholderCapability ? '#67C23A' : '#F56C6C'" class="mr-2">
                <component :is="selectedProvider.hasCardholderCapability ? 'Check' : 'Close'" />
              </el-icon>
              <span>持卡人管理</span>
            </div>
            <div class="capability-item">
              <el-icon :color="selectedProvider.hasPayMethodCapability ? '#67C23A' : '#F56C6C'" class="mr-2">
                <component :is="selectedProvider.hasPayMethodCapability ? 'Check' : 'Close'" />
              </el-icon>
              <span>付款方式</span>
            </div>
            <div class="capability-item">
              <el-icon :color="selectedProvider.hasPayeeCapability ? '#67C23A' : '#F56C6C'" class="mr-2">
                <component :is="selectedProvider.hasPayeeCapability ? 'Check' : 'Close'" />
              </el-icon>
              <span>收款方管理</span>
            </div>
            <div class="capability-item">
              <el-icon :color="selectedProvider.hasPaymentCapability ? '#67C23A' : '#F56C6C'" class="mr-2">
                <component :is="selectedProvider.hasPaymentCapability ? 'Check' : 'Close'" />
              </el-icon>
              <span>付款管理</span>
            </div>
          </div>
        </div>

        <!-- 具体功能列表 -->
        <div>
          <h3 class="text-lg font-semibold mb-3">具体功能 ({{ selectedProvider.supportedFeatures?.length || 0 }}项)</h3>
          <div class="feature-list">
            <el-tag 
              v-for="feature in selectedProvider.supportedFeatures" 
              :key="feature"
              class="mr-2 mb-2"
              type="success"
            >
              {{ feature }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 错误详情对话框 -->
    <el-dialog 
      v-model="errorDialogVisible" 
      :title="`${selectedProvider?.providerName} - 错误信息`"
      width="500px"
    >
      <div v-if="selectedProvider">
        <el-alert 
          :title="selectedProvider.errorMessage || '未知错误'" 
          type="error" 
          :closable="false"
          show-icon
        />
      </div>
    </el-dialog>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Check, Close } from '@element-plus/icons-vue'
import * as ProviderConfigApi from '@/api/vm/provider'

defineOptions({ name: 'VmProviderCapability' })

const loading = ref(true)
const capabilityList = ref<ProviderConfigApi.ProviderCapabilityVO[]>([])
const detailDialogVisible = ref(false)
const errorDialogVisible = ref(false)
const selectedProvider = ref<ProviderConfigApi.ProviderCapabilityVO>()

/** 获取厂商能力列表 */
const getList = async () => {
  try {
    loading.value = true
    const data = await ProviderConfigApi.getProviderCapabilities()
    capabilityList.value = data
  } finally {
    loading.value = false
  }
}

/** 显示功能详情 */
const showFeatureDetail = (row: ProviderConfigApi.ProviderCapabilityVO) => {
  selectedProvider.value = row
  detailDialogVisible.value = true
}

/** 显示错误详情 */
const showErrorDetail = (row: ProviderConfigApi.ProviderCapabilityVO) => {
  selectedProvider.value = row
  errorDialogVisible.value = true
}

/** 初始化 */
onMounted(async () => {
  await getList()
})
</script>

<style scoped>
.capability-table {
  border-radius: 8px;
  overflow: hidden;
}

.capability-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.feature-list {
  max-height: 300px;
  overflow-y: auto;
}
</style>
