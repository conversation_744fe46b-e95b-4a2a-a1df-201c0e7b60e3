<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="卡号" prop="cardNumber">
        <el-input
          v-model="queryParams.cardNumber"
          placeholder="请输入卡号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="持卡人姓名" prop="holderName">
        <el-input
          v-model="queryParams.holderName"
          placeholder="请输入持卡人姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="卡状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择卡状态"
          clearable
          class="!w-240px"
        >
          <el-option label="激活" :value="1" />
          <el-option label="禁用" :value="2" />
          <el-option label="删除" :value="3" />
          <el-option label="冻结" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="厂商类型" prop="providerType">
        <el-select
          v-model="queryParams.providerType"
          placeholder="请选择厂商类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions('vm_provider_type')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="卡标签" prop="label">
        <el-input
          v-model="queryParams.label"
          placeholder="请输入卡标签"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" />搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vm:virtual-card:create']"
        >
          <Icon icon="ep:plus" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vm:virtual-card:export']"
        >
          <Icon icon="ep:download" />导出
        </el-button>
        <el-button
          type="warning"
          plain
          @click="handleBatchSync"
          :loading="syncLoading"
          v-hasPermi="['vm:virtual-card:sync']"
        >
          <Icon icon="ep:refresh" />批量同步
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" stripe>
      <el-table-column label="卡片ID" align="center" prop="id" width="80" />
      <el-table-column label="卡号" align="center" prop="cardNumber" width="180" />
      <el-table-column label="持卡人" align="center" prop="holderName" width="120" />
      <el-table-column label="可用余额" align="center" prop="availableAmount" width="120">
        <template #default="scope">
          <span class="text-green-600 font-bold">${{ scope.row.availableAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="卡状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag type="vm_card_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="厂商" align="center" prop="providerName" width="120" />
      <el-table-column label="卡标签" align="center" prop="label" width="120" />
      <el-table-column label="手机号" align="center" prop="mobile" width="130" />
      <el-table-column label="邮箱" align="center" prop="email" width="180" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180"
      />
      <el-table-column label="操作" align="center" width="300" fixed="right">
        <template #default="scope">
          <div class="flex items-center justify-center">
            <el-button
              type="primary"
              link
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['vm:virtual-card:update']"
            >
              <Icon icon="ep:edit" />修改
            </el-button>
            <el-button
              type="info"
              link
              @click="openDetail(scope.row)"
              v-hasPermi="['vm:virtual-card:query']"
            >
              <Icon icon="ep:view" />详情
            </el-button>
            <el-dropdown
              @command="(command) => handleCommand(command, scope.row)"
              v-hasPermi="[
                'vm:virtual-card:delete',
                'vm:virtual-card:freeze',
                'vm:virtual-card:recharge',
                'vm:virtual-card:sync'
              ]"
            >
              <el-button type="primary" link><Icon icon="ep:d-arrow-right" /> 更多</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    command="handleFreeze"
                    v-if="scope.row.status === 1 && checkPermi(['vm:virtual-card:freeze'])"
                  >
                    <Icon icon="ep:lock" />冻结
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleUnfreeze"
                    v-if="scope.row.status === 4 && checkPermi(['vm:virtual-card:unfreeze'])"
                  >
                    <Icon icon="ep:unlock" />解冻
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleRecharge"
                    v-if="scope.row.status === 1 && checkPermi(['vm:virtual-card:recharge'])"
                  >
                    <Icon icon="ep:coin" />充值
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleRefund"
                    v-if="scope.row.status === 1 && checkPermi(['vm:virtual-card:refund'])"
                  >
                    <Icon icon="ep:money" />退款
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleSync"
                    v-if="checkPermi(['vm:virtual-card:sync'])"
                  >
                    <Icon icon="ep:refresh" />同步
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleTransactions"
                    v-if="checkPermi(['vm:transaction-record:query'])"
                  >
                    <Icon icon="ep:list" />交易记录
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleDelete"
                    v-if="checkPermi(['vm:virtual-card:delete'])"
                  >
                    <Icon icon="ep:delete" />删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <VirtualCardForm ref="formRef" @success="getList" />
  <!-- 详情弹窗 -->
  <VirtualCardDetail ref="detailRef" />
  <!-- 充值/退款弹窗 -->
  <VirtualCardOperation ref="operationRef" @success="getList" />
  <!-- 交易记录弹窗 -->
  <TransactionRecordDialog ref="transactionRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { checkPermi } from '@/utils/permission'
import * as VirtualCardApi from '@/api/vm/card'

import VirtualCardForm from './VirtualCardForm.vue'
import VirtualCardDetail from './VirtualCardDetail.vue'
import VirtualCardOperation from './VirtualCardOperation.vue'
import TransactionRecordDialog from '../transaction/TransactionRecordDialog.vue'

defineOptions({ name: 'VmVirtualCard' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  cardNumber: undefined,
  holderName: undefined,
  status: undefined,
  providerType: undefined,
  label: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const syncLoading = ref(false) // 同步的加载中


/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await VirtualCardApi.getVirtualCardPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 详情操作 */
const detailRef = ref()
const openDetail = (row: VirtualCardApi.VirtualCardVO) => {
  detailRef.value.open(row)
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    exportLoading.value = true
    const data = await VirtualCardApi.exportVirtualCard(queryParams)
    download.excel(data, '虚拟卡数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 批量同步操作 */
const handleBatchSync = async () => {
  try {
    await message.confirm('确认要批量同步虚拟卡数据吗？')
    syncLoading.value = true
    await VirtualCardApi.batchSyncVirtualCards()
    message.success('批量同步成功')
    await getList()
  } catch {
  } finally {
    syncLoading.value = false
  }
}

/** 操作分发 */
const handleCommand = (command: string, row: VirtualCardApi.VirtualCardVO) => {
  switch (command) {
    case 'handleFreeze':
      handleFreeze(row)
      break
    case 'handleUnfreeze':
      handleUnfreeze(row)
      break
    case 'handleRecharge':
      handleRecharge(row)
      break
    case 'handleRefund':
      handleRefund(row)
      break
    case 'handleSync':
      handleSync(row)
      break
    case 'handleTransactions':
      handleTransactions(row)
      break
    case 'handleDelete':
      handleDelete(row.id)
      break
  }
}

/** 冻结操作 */
const handleFreeze = async (row: VirtualCardApi.VirtualCardVO) => {
  try {
    await message.confirm('确认要冻结虚拟卡"' + row.cardNumber + '"吗？')
    await VirtualCardApi.freezeVirtualCard(row.id)
    message.success('冻结成功')
    await getList()
  } catch {}
}

/** 解冻操作 */
const handleUnfreeze = async (row: VirtualCardApi.VirtualCardVO) => {
  try {
    await message.confirm('确认要解冻虚拟卡"' + row.cardNumber + '"吗？')
    await VirtualCardApi.unfreezeVirtualCard(row.id)
    message.success('解冻成功')
    await getList()
  } catch {}
}

/** 充值/退款操作 */
const operationRef = ref()
const handleRecharge = (row: VirtualCardApi.VirtualCardVO) => {
  operationRef.value.open('recharge', row)
}

const handleRefund = (row: VirtualCardApi.VirtualCardVO) => {
  operationRef.value.open('refund', row)
}

/** 同步操作 */
const handleSync = async (row: VirtualCardApi.VirtualCardVO) => {
  try {
    await VirtualCardApi.syncVirtualCard(row.id)
    message.success('同步成功')
    await getList()
  } catch {}
}

/** 交易记录操作 */
const transactionRef = ref()
const handleTransactions = (row: VirtualCardApi.VirtualCardVO) => {
  transactionRef.value.open(row.id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await VirtualCardApi.deleteVirtualCard(id)
    message.success(t('common.delSuccess'))
    await getList()
  } catch {}
}

/** 初始化 */
onMounted(async () => {
  await getList()
  // 厂商类型使用字典数据，无需额外加载
})
</script>
