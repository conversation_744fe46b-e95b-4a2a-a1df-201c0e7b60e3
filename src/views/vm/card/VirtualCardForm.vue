<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="产品编码" prop="productCode" v-if="formType === 'create'">
            <el-input
              v-model="formData.productCode"
              placeholder="请选择产品编码"
              readonly
              @click="openProductSelector"
            >
              <template #append>
                <el-button @click="openProductSelector" :disabled="!formData.providerType">
                  <Icon icon="ep:search" />
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="厂商类型" prop="providerType" v-if="formType === 'create'">
            <el-select v-model="formData.providerType" placeholder="请选择厂商类型" class="w-full">
              <el-option
                v-for="dict in getIntDictOptions('vm_provider_type')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="持卡人名" prop="firstName" v-if="formType === 'create'">
            <el-input v-model="formData.firstName" placeholder="请输入持卡人名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="持卡人姓" prop="lastName" v-if="formType === 'create'">
            <el-input v-model="formData.lastName" placeholder="请输入持卡人姓" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="充值金额" prop="amount" v-if="formType === 'create'">
            <el-input-number
              v-model="formData.amount"
              :min="0.01"
              :precision="2"
              placeholder="请输入充值金额"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="卡标签" prop="label">
            <el-input v-model="formData.label" placeholder="请输入卡标签" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="区号" prop="areaCode">
            <el-input v-model="formData.areaCode" placeholder="请输入区号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="formData.mobile" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="邮箱" prop="email">
        <el-input v-model="formData.email" placeholder="请输入邮箱" />
      </el-form-item>

      <el-form-item label="地址信息" prop="cardAddress">
        <el-input
          v-model="formData.cardAddress"
          type="textarea"
          :rows="3"
          placeholder="请输入地址信息（JSON格式）"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 产品编码选择器 -->
  <ProductCodeSelector
    v-model="productSelectorVisible"
    :provider-type="formData.providerType"
    @confirm="handleProductSelect"
  />
</template>

<script setup lang="ts">
import { getIntDictOptions } from '@/utils/dict'
import * as VirtualCardApi from '@/api/vm/card'
import ProductCodeSelector from './components/ProductCodeSelector.vue'

defineOptions({ name: 'VirtualCardForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const productSelectorVisible = ref(false) // 产品编码选择器是否展示
const formData = ref({
  id: undefined,
  productCode: '',
  firstName: '',
  lastName: '',
  amount: undefined,
  label: '',
  areaCode: '',
  mobile: '',
  email: '',
  cardAddress: '',
  providerType: undefined,
  remark: ''
})
const formRules = reactive({
  productCode: [{ required: true, message: '产品编码不能为空', trigger: 'blur' }],
  firstName: [{ required: true, message: '持卡人名不能为空', trigger: 'blur' }],
  lastName: [{ required: true, message: '持卡人姓不能为空', trigger: 'blur' }],
  amount: [{ required: true, message: '充值金额不能为空', trigger: 'blur' }],
  email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref


/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '添加虚拟卡' : '修改虚拟卡'
  formType.value = type
  resetForm()
  
  // 厂商类型使用字典数据，无需额外加载
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await VirtualCardApi.getVirtualCard(id)
      formData.value = data
    } finally {
      formLoading.value = false
    }
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value?.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as VirtualCardApi.VirtualCardCreateReqVO | VirtualCardApi.VirtualCardUpdateReqVO
    if (formType.value === 'create') {
      await VirtualCardApi.createVirtualCard(data as VirtualCardApi.VirtualCardCreateReqVO)
      message.success(t('common.createSuccess'))
    } else {
      await VirtualCardApi.updateVirtualCard(data as VirtualCardApi.VirtualCardUpdateReqVO)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    productCode: '',
    firstName: '',
    lastName: '',
    amount: undefined,
    label: '',
    areaCode: '',
    mobile: '',
    email: '',
    cardAddress: '',
    providerType: undefined,
    remark: ''
  }
  formRef.value?.resetFields()
}

/** 打开产品编码选择器 */
const openProductSelector = () => {
  if (!formData.value.providerType) {
    message.warning('请先选择厂商类型')
    return
  }
  productSelectorVisible.value = true
}

/** 处理产品选择 */
const handleProductSelect = (product: VirtualCardApi.CardProductVO) => {
  formData.value.productCode = product.productCode
  // 可以根据需要设置其他字段
  message.success(`已选择产品编码: ${product.productCode}`)
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
