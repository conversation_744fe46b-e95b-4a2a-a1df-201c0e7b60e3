<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择产品编码"
    width="800px"
    :close-on-click-modal="false"
  >
    <!-- 搜索栏 -->
    <div class="mb-4">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchForm.productCode"
            placeholder="请输入产品编码"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :span="8">
          <el-select v-model="searchForm.type" placeholder="请选择卡类型" clearable>
            <el-option label="储值卡" value="save" />
            <el-option label="额度卡" value="share" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select v-model="searchForm.network" placeholder="请选择卡网络" clearable>
            <el-option label="VISA" value="VISA" />
            <el-option label="MASTERCARD" value="MASTERCARD" />
          </el-select>
        </el-col>
      </el-row>
      <div class="mt-2">
        <el-button type="primary" @click="handleSearch" :loading="loading">
          <Icon icon="ep:search" class="mr-1" />
          搜索
        </el-button>
        <el-button @click="resetSearch">
          <Icon icon="ep:refresh" class="mr-1" />
          重置
        </el-button>
      </div>
    </div>

    <!-- 产品卡片列表 -->
    <div v-loading="loading" class="product-grid">
      <div
        v-for="product in filteredProducts"
        :key="product.productCode"
        class="product-card"
        :class="{ 'selected': selectedProduct?.productCode === product.productCode }"
        @click="selectProduct(product)"
      >
        <div class="product-header">
          <div class="product-code">{{ product.productCode }}</div>
          <el-tag :type="getCardTypeTagType(product.type)" size="small">
            {{ getCardTypeText(product.type) }}
          </el-tag>
        </div>
        
        <div class="product-info">
          <div class="info-row">
            <span class="label">BIN:</span>
            <span class="value">{{ product.bin }}</span>
          </div>
          <div class="info-row">
            <span class="label">网络:</span>
            <span class="value">{{ product.network }}</span>
          </div>
          <div class="info-row">
            <span class="label">介质:</span>
            <span class="value">{{ getMediaText(product.media) }}</span>
          </div>
          <div class="info-row">
            <span class="label">发卡地区:</span>
            <span class="value">{{ product.issuingArea }}</span>
          </div>
        </div>

        <!-- 选中标识 -->
        <div v-if="selectedProduct?.productCode === product.productCode" class="selected-icon">
          <Icon icon="ep:check" />
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!loading && filteredProducts.length === 0" description="暂无产品编码数据" />

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!selectedProduct">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import * as CardApi from '@/api/vm/card'

defineOptions({ name: 'ProductCodeSelector' })

interface Props {
  modelValue: boolean
  providerType?: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', product: CardApi.CardProductVO): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  providerType: undefined
})

const emit = defineEmits<Emits>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const productList = ref<CardApi.CardProductVO[]>([])
const selectedProduct = ref<CardApi.CardProductVO>()

const searchForm = ref({
  productCode: '',
  type: '',
  network: ''
})

// 过滤后的产品列表
const filteredProducts = computed(() => {
  let filtered = productList.value
  
  if (searchForm.value.productCode) {
    filtered = filtered.filter(item => 
      item.productCode.toLowerCase().includes(searchForm.value.productCode.toLowerCase())
    )
  }
  
  if (searchForm.value.type) {
    filtered = filtered.filter(item => item.type === searchForm.value.type)
  }
  
  if (searchForm.value.network) {
    filtered = filtered.filter(item => item.network === searchForm.value.network)
  }
  
  return filtered
})

// 监听弹窗打开，加载数据
watch(dialogVisible, (visible) => {
  if (visible) {
    loadProductCodes()
  } else {
    // 关闭时重置状态
    selectedProduct.value = undefined
    resetSearch()
  }
})

/** 加载产品编码列表 */
const loadProductCodes = async () => {
  try {
    loading.value = true
    const data = await CardApi.getProductCodes(props.providerType)
    productList.value = data || []
  } catch (error) {
    console.error('加载产品编码失败:', error)
    ElMessage.error('加载产品编码失败')
  } finally {
    loading.value = false
  }
}

/** 搜索 */
const handleSearch = () => {
  // 搜索逻辑已在 computed 中处理
}

/** 重置搜索 */
const resetSearch = () => {
  searchForm.value = {
    productCode: '',
    type: '',
    network: ''
  }
}

/** 选择产品 */
const selectProduct = (product: CardApi.CardProductVO) => {
  selectedProduct.value = product
}

/** 确认选择 */
const handleConfirm = () => {
  if (selectedProduct.value) {
    emit('confirm', selectedProduct.value)
    dialogVisible.value = false
  }
}

/** 取消 */
const handleCancel = () => {
  dialogVisible.value = false
}

/** 获取卡类型标签类型 */
const getCardTypeTagType = (type: string) => {
  switch (type) {
    case 'save': return 'success'
    case 'share': return 'warning'
    default: return 'info'
  }
}

/** 获取卡类型文本 */
const getCardTypeText = (type: string) => {
  switch (type) {
    case 'save': return '储值卡'
    case 'share': return '额度卡'
    default: return type
  }
}

/** 获取介质文本 */
const getMediaText = (media: string) => {
  switch (media) {
    case 'virtual_card': return '虚拟卡'
    case 'physical_card': return '实体卡'
    default: return media
  }
}
</script>

<style scoped>
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.product-card {
  position: relative;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
}

.product-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.product-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.product-code {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.product-info {
  space-y: 8px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.label {
  color: #909399;
  font-size: 14px;
}

.value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.selected-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}
</style>
