<template>
  <Dialog title="虚拟卡详情" v-model="dialogVisible" width="800px">
    <el-descriptions :column="2" border v-loading="loading">
      <el-descriptions-item label="卡片ID">
        {{ cardData.id }}
      </el-descriptions-item>
      <el-descriptions-item label="第三方卡片ID">
        {{ cardData.thirdPartyCardId }}
      </el-descriptions-item>
      <el-descriptions-item label="卡号">
        <span class="font-mono text-blue-600">{{ cardData.cardNumber }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="CVV">
        <span class="font-mono text-red-600">{{ cardData.cvv }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="有效期">
        <span class="font-mono">{{ cardData.expireDate }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="持卡人姓名">
        {{ cardData.holderName }}
      </el-descriptions-item>
      <el-descriptions-item label="可用余额">
        <span class="text-green-600 font-bold text-lg">${{ cardData.availableAmount }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="卡类型">
        {{ cardData.cardType }}
      </el-descriptions-item>
      <el-descriptions-item label="卡状态">
        <dict-tag :type="DICT_TYPE.VM_CARD_STATUS" :value="cardData.status" />
      </el-descriptions-item>
      <el-descriptions-item label="厂商">
        {{ cardData.providerName }}
      </el-descriptions-item>
      <el-descriptions-item label="产品编码">
        {{ cardData.productCode }}
      </el-descriptions-item>
      <el-descriptions-item label="卡标签">
        <el-tag v-if="cardData.label" type="info">{{ cardData.label }}</el-tag>
        <span v-else>-</span>
      </el-descriptions-item>
      <el-descriptions-item label="手机号">
        {{ cardData.mobile || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="邮箱">
        {{ cardData.email || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="地址信息" :span="2">
        <el-input
          v-if="cardData.cardAddress"
          :model-value="formatAddress(cardData.cardAddress)"
          type="textarea"
          :rows="3"
          readonly
        />
        <span v-else>-</span>
      </el-descriptions-item>
      <el-descriptions-item label="第三方创建时间">
        {{ formatDate(cardData.thirdPartyCreateTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="最后同步时间">
        {{ formatDate(cardData.lastSyncTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDate(cardData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="备注" :span="2">
        {{ cardData.remark || '-' }}
      </el-descriptions-item>
    </el-descriptions>

    <template #footer>
      <div class="flex justify-between">
        <div>
          <el-button
            type="primary"
            @click="handleSync"
            :loading="syncLoading"
            v-hasPermi="['vm:virtual-card:sync']"
          >
            <Icon icon="ep:refresh" />同步数据
          </el-button>
          <el-button
            type="info"
            @click="handleTransactions"
            v-hasPermi="['vm:transaction-record:query']"
          >
            <Icon icon="ep:list" />交易记录
          </el-button>
        </div>
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </div>
    </template>
  </Dialog>

  <!-- 交易记录弹窗 -->
  <!-- <TransactionRecordDialog ref="transactionRef" /> -->
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import * as VirtualCardApi from '@/api/vm/card'
// import TransactionRecordDialog from '../transaction/TransactionRecordDialog.vue'

defineOptions({ name: 'VirtualCardDetail' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const loading = ref(false) // 加载中
const syncLoading = ref(false) // 同步加载中
const cardData = ref<VirtualCardApi.VirtualCardVO>({} as VirtualCardApi.VirtualCardVO)

/** 打开弹窗 */
const open = (data: VirtualCardApi.VirtualCardVO) => {
  dialogVisible.value = true
  cardData.value = data
}

/** 格式化地址信息 */
const formatAddress = (address: string) => {
  try {
    const addr = JSON.parse(address)
    return JSON.stringify(addr, null, 2)
  } catch {
    return address
  }
}

/** 同步数据 */
const handleSync = async () => {
  try {
    syncLoading.value = true
    await VirtualCardApi.syncVirtualCard(cardData.value.id)
    message.success('同步成功')
    
    // 重新获取数据
    const data = await VirtualCardApi.getVirtualCard(cardData.value.id)
    cardData.value = data
  } catch {
  } finally {
    syncLoading.value = false
  }
}

/** 查看交易记录 */
const transactionRef = ref()
const handleTransactions = () => {
  // transactionRef.value.open(cardData.value.id)
  message.info('交易记录功能开发中...')
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
