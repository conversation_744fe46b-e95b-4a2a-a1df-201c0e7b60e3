<template>
  <Dialog title="交易记录" v-model="dialogVisible" width="1200px">
    <el-table v-loading="loading" :data="list" stripe max-height="500">
      <el-table-column label="交易ID" align="center" prop="id" width="80" />
      <el-table-column label="交易类型" align="center" prop="transactionTypeName" width="100" />
      <el-table-column label="交易金额" align="center" prop="amount" width="120">
        <template #default="scope">
          <span class="text-blue-600 font-bold">${{ scope.row.amount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交易前余额" align="center" prop="balanceBefore" width="120">
        <template #default="scope">
          <span v-if="scope.row.balanceBefore !== null">${{ scope.row.balanceBefore }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="交易后余额" align="center" prop="balanceAfter" width="120">
        <template #default="scope">
          <span v-if="scope.row.balanceAfter !== null">${{ scope.row.balanceAfter }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="交易状态" align="center" prop="statusName" width="100">
        <template #default="scope">
          <el-tag
            :type="scope.row.status === 1 ? 'success' : scope.row.status === 2 ? 'danger' : 'warning'"
          >
            {{ scope.row.statusName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="交易描述" align="center" prop="description" width="150" />
      <el-table-column
        label="交易时间"
        align="center"
        prop="thirdPartyTransactionTime"
        :formatter="dateFormatter"
        width="180"
      />
      <el-table-column label="操作" align="center" width="120">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="openDetail(scope.row)"
          >
            <Icon icon="ep:view" />详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>

  <!-- 详情弹窗 -->
  <TransactionRecordDetail ref="detailRef" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import * as TransactionRecordApi from '@/api/vm/transaction'
import TransactionRecordDetail from './TransactionRecordDetail.vue'

defineOptions({ name: 'TransactionRecordDialog' })

const dialogVisible = ref(false) // 弹窗的是否展示
const loading = ref(false) // 加载中
const list = ref<TransactionRecordApi.TransactionRecordVO[]>([]) // 交易记录列表

/** 打开弹窗 */
const open = async (cardId: number) => {
  dialogVisible.value = true
  loading.value = true
  try {
    list.value = await TransactionRecordApi.getTransactionRecordsByCardId(cardId)
  } finally {
    loading.value = false
  }
}

/** 详情操作 */
const detailRef = ref()
const openDetail = (row: TransactionRecordApi.TransactionRecordVO) => {
  detailRef.value.open(row)
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
