<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="虚拟卡ID" prop="cardId">
        <el-input
          v-model="queryParams.cardId"
          placeholder="请输入虚拟卡ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="第三方卡片ID" prop="thirdPartyCardId">
        <el-input
          v-model="queryParams.thirdPartyCardId"
          placeholder="请输入第三方卡片ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="交易类型" prop="transactionType">
        <el-select
          v-model="queryParams.transactionType"
          placeholder="请选择交易类型"
          clearable
          class="!w-240px"
        >
          <el-option label="创建卡片" :value="1" />
          <el-option label="充值" :value="2" />
          <el-option label="退款" :value="3" />
          <el-option label="冻结" :value="4" />
          <el-option label="解冻" :value="5" />
          <el-option label="删除卡片" :value="6" />
          <el-option label="消费" :value="7" />
          <el-option label="冲正" :value="8" />
        </el-select>
      </el-form-item>
      <el-form-item label="交易状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择交易状态"
          clearable
          class="!w-240px"
        >
          <el-option label="成功" :value="1" />
          <el-option label="失败" :value="2" />
          <el-option label="处理中" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="厂商类型" prop="providerType">
        <el-select
          v-model="queryParams.providerType"
          placeholder="请选择厂商类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="provider in providerTypes"
            :key="provider.type"
            :label="provider.name"
            :value="provider.type"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" />搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vm:transaction-record:export']"
        >
          <Icon icon="ep:download" />导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" stripe>
      <el-table-column label="交易ID" align="center" prop="id" width="80" />
      <el-table-column label="虚拟卡ID" align="center" prop="cardId" width="100" />
      <el-table-column label="第三方卡片ID" align="center" prop="thirdPartyCardId" width="180" />
      <el-table-column label="交易类型" align="center" prop="transactionTypeName" width="100" />
      <el-table-column label="交易金额" align="center" prop="amount" width="120">
        <template #default="scope">
          <span class="text-blue-600 font-bold">${{ scope.row.amount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交易前余额" align="center" prop="balanceBefore" width="120">
        <template #default="scope">
          <span v-if="scope.row.balanceBefore !== null">${{ scope.row.balanceBefore }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="交易后余额" align="center" prop="balanceAfter" width="120">
        <template #default="scope">
          <span v-if="scope.row.balanceAfter !== null">${{ scope.row.balanceAfter }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="交易状态" align="center" prop="statusName" width="100">
        <template #default="scope">
          <el-tag
            :type="scope.row.status === 1 ? 'success' : scope.row.status === 2 ? 'danger' : 'warning'"
          >
            {{ scope.row.statusName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="厂商" align="center" prop="providerName" width="120" />
      <el-table-column label="交易描述" align="center" prop="description" width="150" />
      <el-table-column
        label="交易时间"
        align="center"
        prop="thirdPartyTransactionTime"
        :formatter="dateFormatter"
        width="180"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180"
      />
      <el-table-column label="操作" align="center" width="120" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="openDetail(scope.row)"
            v-hasPermi="['vm:transaction-record:query']"
          >
            <Icon icon="ep:view" />详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 详情弹窗 -->
  <TransactionRecordDetail ref="detailRef" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as TransactionRecordApi from '@/api/vm/transaction'

import TransactionRecordDetail from './TransactionRecordDetail.vue'

defineOptions({ name: 'VmTransactionRecord' })

const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  cardId: undefined,
  thirdPartyCardId: undefined,
  transactionType: undefined,
  status: undefined,
  providerType: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中


/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TransactionRecordApi.getTransactionRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 详情操作 */
const detailRef = ref()
const openDetail = (row: TransactionRecordApi.TransactionRecordVO) => {
  detailRef.value.open(row)
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    exportLoading.value = true
    const data = await TransactionRecordApi.exportTransactionRecord(queryParams)
    download.excel(data, '交易记录数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 */
onMounted(async () => {
  await getList()
  // 厂商类型使用字典数据，无需额外加载
})
</script>
